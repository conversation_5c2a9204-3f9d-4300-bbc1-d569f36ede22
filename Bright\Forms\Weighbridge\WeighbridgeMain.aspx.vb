Imports System.Net.Http
Imports System.Threading.Tasks
Imports Newtonsoft.Json
Imports System.Text
Imports Bright.Domain

Public Class WeighbridgeMain
    Inherits System.Web.UI.Page

    ' Properties
    Private Shared ReadOnly httpClient As New HttpClient()
    'Private apiBaseUrl As String = "http://127.0.0.1:8085"
    Private apiBaseUrl As String = "http://127.0.0.1:8082"
    Private isConnected As Boolean = False
    Private sessionStartTime As DateTime = DateTime.Now
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            InitializePage()
        Else
            ' Handle postback events
            HandlePostBackEvent()
        End If
    End Sub    
    Private Sub HandlePostBackEvent()
        Dim eventTarget = Request.Form("__EVENTTARGET")
        Dim eventArgument = Request.Form("__EVENTARGUMENT")

        If eventTarget = Me.ClientID AndAlso Not String.IsNullOrEmpty(eventArgument) Then
            Try
                If eventArgument.StartsWith("SAVE_TRUCK_ENTRY:") Then
                    Dim jsonData = eventArgument.Substring("SAVE_TRUCK_ENTRY:".Length)
                    HandleTruckEntry(jsonData)
                ElseIf eventArgument.StartsWith("SEARCH_TRUCK:") Then
                    Dim vehicleId = eventArgument.Substring("SEARCH_TRUCK:".Length)
                    HandleSearchTruck(vehicleId)
                ElseIf eventArgument.StartsWith("SAVE_TRUCK_EXIT:") Then
                    Dim jsonData = eventArgument.Substring("SAVE_TRUCK_EXIT:".Length)
                    HandleTruckExit(jsonData)
                ElseIf eventArgument.StartsWith("SAVE_NEW_VEHICLE:") Then
                    Dim jsonData = eventArgument.Substring("SAVE_NEW_VEHICLE:".Length)
                    HandleSaveNewVehicle(jsonData)
                ElseIf eventArgument = "REFRESH_HISTORY" Then
                    HandleRefreshHistory()
                ElseIf eventArgument = "EXPORT_EXCEL" Then
                    HandleExportExcel()
                End If
            Catch ex As Exception
                ShowAlert("Error processing request: " & ex.Message, "danger")
            End Try
        End If
    End Sub    
    Private Sub InitializePage()
        Try
            ' Load settings from session or config
            LoadSettings()

            ' Load vehicles for dropdowns
            LoadVehicles()

            ' Log initialization
            AddLogEntry("System", "Weighbridge interface initialized")

            ' Try to connect automatically
            Task.Run(Async Sub() Await CheckServiceStatusAsync())
        Catch ex As Exception
            ShowAlert("Error during initialization: " & ex.Message, "danger")
            AddLogEntry("Error", "Initialization failed: " & ex.Message)
        End Try
    End Sub

    Private Sub LoadSettings()
        Try
            ' Load from session or use defaults
            If Session("ApiUrl") IsNot Nothing Then
                apiBaseUrl = Session("ApiUrl").ToString()
                ApiUrlTextBox.Text = apiBaseUrl
            End If

            If Session("RefreshInterval") IsNot Nothing Then
                RefreshIntervalTextBox.Text = Session("RefreshInterval").ToString()
            End If
        Catch ex As Exception
            AddLogEntry("Warning", "Could not load saved settings: " & ex.Message)
        End Try
    End Sub
    Protected Sub ConnectButton_Click(sender As Object, e As EventArgs) Handles ConnectButton.Click
        Page.RegisterAsyncTask(New PageAsyncTask(AddressOf ConnectToServiceTask))
    End Sub

    Private Async Function ConnectToServiceTask() As Task
        Try
            ShowAlert("Connecting to WeighbridgeService...", "warning")
            AddLogEntry("User", "Attempting to connect to service")

            Dim success = Await ConnectToServiceAsync()

            If success Then
                isConnected = True
                ConnectButton.Enabled = False
                DisconnectButton.Enabled = True
                ShowAlert("Successfully connected to WeighbridgeService!", "success")
                AddLogEntry("System", "Connected to service at " & apiBaseUrl)

                ' Load initial data
                Await RefreshDataAsync()
            Else
                ShowAlert("Failed to connect to WeighbridgeService. Please check if the service is running.", "danger")
                AddLogEntry("Error", "Connection failed")
            End If
        Catch ex As Exception
            ShowAlert("Connection error: " & ex.Message, "danger")
            AddLogEntry("Error", "Connection error: " & ex.Message)
        End Try
    End Function

    Protected Sub DisconnectButton_Click(sender As Object, e As EventArgs) Handles DisconnectButton.Click
        Try
            isConnected = False
            ConnectButton.Enabled = True
            DisconnectButton.Enabled = False

            ' Reset display
            WeightLabel.Text = "0.00"
            StabilityLabel.Text = "Disconnected"

            ShowAlert("Disconnected from WeighbridgeService", "warning")
            AddLogEntry("User", "Disconnected from service")

            ' Update UI via JavaScript
            Dim script = "
                document.getElementById('statusIndicator').className = 'status-indicator status-disconnected';
                document.getElementById('connectionStatus').textContent = 'Disconnected';
                document.getElementById('serviceStatus').textContent = 'Disconnected';
                document.getElementById('lastUpdate').textContent = 'Disconnected';
            "
            ClientScript.RegisterStartupScript(Me.GetType(), "UpdateStatus", script, True)
        Catch ex As Exception
            ShowAlert("Error during disconnection: " & ex.Message, "danger")
        End Try
    End Sub
    Protected Sub TestConnectionButton_Click(sender As Object, e As EventArgs) Handles TestConnectionButton.Click
        Page.RegisterAsyncTask(New PageAsyncTask(AddressOf TestConnectionTask))
    End Sub

    Private Async Function TestConnectionTask() As Task
        Try
            AddLogEntry("User", "Testing connection to service")

            Using response = Await httpClient.GetAsync(apiBaseUrl & "/api/scale/TestConnection")
                If response.IsSuccessStatusCode Then
                    Dim result = Await response.Content.ReadAsStringAsync()
                    ShowAlert("Connection test successful! " & result, "success")
                    AddLogEntry("Test", "Connection test passed")
                Else
                    ShowAlert("Connection test failed. Status: " & response.StatusCode.ToString(), "warning")
                    AddLogEntry("Test", "Connection test failed: " & response.StatusCode.ToString())
                End If
            End Using
        Catch ex As Exception
            ShowAlert("Connection test error: " & ex.Message, "danger")
            AddLogEntry("Error", "Connection test error: " & ex.Message)
        End Try
    End Function
    Protected Sub RefreshButton_Click(sender As Object, e As EventArgs) Handles RefreshButton.Click
        If isConnected Then
            Page.RegisterAsyncTask(New PageAsyncTask(AddressOf RefreshDataAsync))
        Else
            ShowAlert("Not connected to service. Please connect first.", "warning")
        End If
    End Sub

    Protected Sub ZeroScaleButton_Click(sender As Object, e As EventArgs) Handles ZeroScaleButton.Click
        ' Placeholder for zero scale functionality
        ShowAlert("Zero scale command sent (functionality to be implemented)", "warning")
        AddLogEntry("User", "Zero scale command initiated")
    End Sub

    Protected Sub ClearLogButton_Click(sender As Object, e As EventArgs) Handles ClearLogButton.Click
        Try
            LogPanel.Controls.Clear()
            LogPanel.Controls.Add(New LiteralControl("<div class='log-entry'><span class='timestamp'>[System]</span> Log cleared</div>"))
            AddLogEntry("User", "Activity log cleared")
        Catch ex As Exception
            ShowAlert("Error clearing log: " & ex.Message, "danger")
        End Try
    End Sub

    Protected Sub SaveSettingsButton_Click(sender As Object, e As EventArgs) Handles SaveSettingsButton.Click
        Try
            ' Save settings to session
            Session("ApiUrl") = ApiUrlTextBox.Text.Trim()
            Session("RefreshInterval") = RefreshIntervalTextBox.Text.Trim()

            ' Update local variables
            apiBaseUrl = ApiUrlTextBox.Text.Trim()

            ShowAlert("Settings saved successfully!", "success")
            AddLogEntry("User", "Settings updated - API URL: " & apiBaseUrl & ", Refresh: " & RefreshIntervalTextBox.Text & "ms")
        Catch ex As Exception
            ShowAlert("Error saving settings: " & ex.Message, "danger")
            AddLogEntry("Error", "Settings save failed: " & ex.Message)
        End Try
    End Sub
    Private Async Function ConnectToServiceAsync() As Task(Of Boolean)
        Try
            Using response = Await httpClient.GetAsync(apiBaseUrl)
                If response.IsSuccessStatusCode Then
                    Dim content = Await response.Content.ReadAsStringAsync()

                    ' Update UI with service info
                    Dim script = "
                        document.getElementById('statusIndicator').className = 'status-indicator status-stable';
                        document.getElementById('connectionStatus').textContent = 'Connected';
                        document.getElementById('serviceStatus').textContent = 'Online';
                        document.getElementById('apiEndpoint').textContent = '" & apiBaseUrl & "';
                    "
                    ClientScript.RegisterStartupScript(Me.GetType(), "UpdateConnectionStatus", script, True)

                    Return True
                End If
            End Using
        Catch ex As Exception
            AddLogEntry("Error", "Service connection failed: " & ex.Message)
        End Try

        Return False
    End Function
    Private Async Function CheckServiceStatusAsync() As Task
        Try
            Using response = Await httpClient.GetAsync(apiBaseUrl)
                If response.IsSuccessStatusCode Then
                    ' Service is running, update status
                    Dim script = "
                        document.getElementById('serviceStatus').textContent = 'Available';
                        document.getElementById('connectionType').textContent = 'API';
                    "
                    ClientScript.RegisterStartupScript(Me.GetType(), "ServiceStatus", script, True)
                End If
            End Using
        Catch
            ' Service not available
        End Try
    End Function

    Private Async Function RefreshDataAsync() As Task
        Try
            ' Get current weight
            Using response = Await httpClient.GetAsync(apiBaseUrl & "/api/scale/GetCurrentWeight")
                If response.IsSuccessStatusCode Then
                    Dim jsonContent = Await response.Content.ReadAsStringAsync()
                    Dim weightData = JsonConvert.DeserializeObject(Of WeightData)(jsonContent)

                    ' Update display
                    WeightLabel.Text = weightData.Weight.ToString("F2")
                    StabilityLabel.Text = If(weightData.IsStable, "Stable ✓", "Unstable ⚠")

                    ' Update JavaScript
                    Dim statusClass = If(weightData.IsStable, "status-stable", "status-unstable")
                    Dim script = $"
                        document.getElementById('statusIndicator').className = 'status-indicator {statusClass}';
                        document.getElementById('connectionStatus').textContent = '{If(weightData.IsStable, "Stable", "Reading...")}';
                        document.getElementById('lastUpdate').textContent = '{DateTime.Now.ToString("HH:mm:ss")}';
                        totalReadings++;
                        {If(weightData.IsStable, "stableReadings++;", "")}
                    "
                    ClientScript.RegisterStartupScript(Me.GetType(), "UpdateWeight", script, True)

                    AddLogEntry("Data", $"Weight: {weightData.Weight:F2} kg, Stable: {weightData.IsStable}")
                Else
                    AddLogEntry("Warning", "Failed to retrieve weight data: " & response.StatusCode.ToString())
                End If
            End Using

            ' Get scale status
            Using response = Await httpClient.GetAsync(apiBaseUrl & "/api/scale/GetStatus")
                If response.IsSuccessStatusCode Then
                    Dim statusContent = Await response.Content.ReadAsStringAsync()
                    Dim statusData = JsonConvert.DeserializeObject(Of ScaleStatus)(statusContent)

                    ' Update scale configuration display
                    Dim script = $"
                        document.getElementById('scalePort').textContent = '{statusData.PortName}';
                        document.getElementById('connectionType').textContent = '{statusData.ConnectionType}';
                    "
                    ClientScript.RegisterStartupScript(Me.GetType(), "UpdateStatus", script, True)
                End If
            End Using

        Catch ex As Exception
            AddLogEntry("Error", "Data refresh failed: " & ex.Message)
            ShowAlert("Error refreshing data: " & ex.Message, "danger")
        End Try
    End Function

    Private Sub ShowAlert(message As String, alertType As String)
        AlertLabel.Text = message
        AlertPanel.Visible = True

        ' Set CSS class based on type
        Dim cssClass = "alert alert-" & alertType
        ClientScript.RegisterStartupScript(Me.GetType(), "SetAlertClass",
            $"document.getElementById('alertMessage').className = '{cssClass}';", True)

        ' Auto-hide after 5 seconds
        ClientScript.RegisterStartupScript(Me.GetType(), "HideAlert",
            "setTimeout(function() { document.getElementById('" & AlertPanel.ClientID & "').style.display = 'none'; }, 5000);", True)
    End Sub

    Private Sub AddLogEntry(category As String, message As String)
        Try
            Dim timestamp = DateTime.Now.ToString("HH:mm:ss")
            Dim logEntry = $"<div class='log-entry'><span class='timestamp'>[{timestamp}] [{category}]</span> {message}</div>"

            ' Add to top of log
            Dim existingContent = ""
            For Each control As Control In LogPanel.Controls
                If TypeOf control Is LiteralControl Then
                    existingContent &= DirectCast(control, LiteralControl).Text
                End If
            Next

            LogPanel.Controls.Clear()
            LogPanel.Controls.Add(New LiteralControl(logEntry & existingContent))

            ' Keep only last 50 entries
            Dim entries = existingContent.Split(New String() {"<div class='log-entry'>"}, StringSplitOptions.RemoveEmptyEntries)
            If entries.Length > 50 Then
                ' Truncate old entries
                Dim truncated = String.Join("<div class='log-entry'>", entries.Take(50))
                LogPanel.Controls.Clear()
                LogPanel.Controls.Add(New LiteralControl(logEntry & "<div class='log-entry'>" & truncated))
            End If
        Catch ex As Exception
            ' Fail silently for logging errors
        End Try
    End Sub

    ' Data classes for JSON deserialization
    Public Class WeightData
        Public Property Weight As Decimal
        Public Property IsStable As Boolean
        Public Property Timestamp As DateTime
        Public Property Unit As String = "kg"
    End Class
    Public Class ScaleStatus
        Public Property IsConnected As Boolean
        Public Property PortName As String
        Public Property BaudRate As Integer
        Public Property ConnectionType As String
        Public Property LastReading As DateTime
        Public Property ErrorMessage As String
    End Class

    ' Truck Management Methods
    Private Sub HandleTruckEntry(jsonData As String)
        Try
            Dim data = JsonConvert.DeserializeObject(Of Dictionary(Of String, Object))(jsonData)
            Dim vehicleId = Convert.ToInt32(data("vehicleId"))
            AddLogEntry("Debug", $"Processing truck entry: Vehicle ID {vehicleId}")

            ' Create weighbridge controller
            Using db As New BrightEntities()
                Try
                    AddLogEntry("Debug", "Database context created successfully")
                    
                    ' Get vehicle record
                    Dim vehicle = db.tm_kendaraan.FirstOrDefault(Function(k) k.Id = vehicleId)
                    If vehicle Is Nothing Then
                        Throw New Exception($"Vehicle with ID {vehicleId} not found")
                    End If

                    AddLogEntry("Debug", $"Found vehicle: {vehicle.NoPolisi}")

                    ' Try to create minimal master data if they don't exist
                    Dim defaultArea = db.tm_area.FirstOrDefault()
                    If defaultArea Is Nothing Then
                        ' Create default area
                        defaultArea = New tm_area() With {
                            .KodeArea = "WB001",
                            .NamaArea = "Weighbridge Area",
                            .Singkatan = "WB",
                            .Aktif = True,
                            .CreatedBy = "System",
                            .CreatedDate = DateTime.Now
                        }
                        db.tm_area.Add(defaultArea)
                        db.SaveChanges()
                        AddLogEntry("Debug", "Created default area")
                    End If

                    Dim defaultCabang = db.tm_cabang.FirstOrDefault()
                    If defaultCabang Is Nothing Then
                        ' Create default cabang  
                        defaultCabang = New tm_cabang() With {
                            .Area_id = defaultArea.Id,
                            .KodeCabang = "WB001",
                            .NamaCabang = "Weighbridge Branch",
                            .IsHeadOffice = True,
                            .Singkatan = "WB",
                            .Alamat = "Weighbridge Location",
                            .Kota = "Default City",
                            .KepalaCabang = "System",
                            .Aktif = True,
                            .CreatedBy = "System",
                            .CreatedDate = DateTime.Now
                        }
                        db.tm_cabang.Add(defaultCabang)
                        db.SaveChanges()
                        AddLogEntry("Debug", "Created default cabang")
                    End If

                    Dim defaultLokasi = db.tm_lokasi.FirstOrDefault()
                    If defaultLokasi Is Nothing Then
                        ' Create default lokasi
                        defaultLokasi = New tm_lokasi() With {
                            .Area_id = defaultArea.Id,
                            .Cabang_id = defaultCabang.Id,
                            .KodeLokasi = "WB001",
                            .NamaLokasi = "Weighbridge Location",
                            .NamaKontak = "System",
                            .Alamat = "Default Weighbridge Address",
                            .Aktif = True,
                            .IsDefault = True,
                            .CreatedBy = "System",
                            .CreatedDate = DateTime.Now
                        }
                        db.tm_lokasi.Add(defaultLokasi)
                        db.SaveChanges()
                        AddLogEntry("Debug", "Created default lokasi")
                    End If

                    AddLogEntry("Debug", $"Using master data - Area: {defaultArea.Id}, Cabang: {defaultCabang.Id}, Lokasi: {defaultLokasi.Id}")                    ' Create new weighbridge transaction
                    Dim transaction As New tr_po_receive_weighbridge() With {
                        .Kendaraan_id = vehicle.Id,
                        .DriverName = data("driverName").ToString(),
                        .Keterangan = data("company").ToString() & " - " & data("material").ToString(),
                        .GrossWeight = Convert.ToDecimal(data("grossWeight")),
                        .WeighInTime = DateTime.Now,
                        .Status = "WEIGH_IN",
                        .Tanggal = DateTime.Today,
                        .NoReceive = GenerateReceiveNumber(),
                        .CreatedBy = "System",
                        .CreatedDate = DateTime.Now,
                        .Area_id = defaultArea.Id,
                        .Cabang_id = defaultCabang.Id,
                        .Lokasi_id = defaultLokasi.Id
                    }

                    ' Add to context and save
                    Try
                        db.tr_po_receive_weighbridge.Add(transaction)
                        ' Get the vehicle number for logging
                        Dim vehicleNumber = vehicle.NoPolisi
                        AddLogEntry("Debug", $"Added transaction to context: Kendaraan_id={transaction.Kendaraan_id} (NoPolisi: {If(vehicleNumber, "N/A")}), Area_id={transaction.Area_id}, Cabang_id={transaction.Cabang_id}, Lokasi_id={transaction.Lokasi_id}")
                        db.SaveChanges()
                        AddLogEntry("Debug", $"Transaction saved successfully with ID: {transaction.Id}")

                        ' Create log entry
                        Dim logEntry As New tr_weighbridge_log() With {
                            .PoReceiveWeighbridge_id = transaction.Id,
                            .WeighType = "GROSS",
                            .Weight = transaction.GrossWeight.Value,
                            .WeighTime = DateTime.Now,
                            .OperatorName = data("operatorName").ToString(),
                            .TruckNumber = If(vehicleNumber, "Unknown"),
                            .ScaleId = "SCALE01",
                            .RawData = jsonData,
                            .IsActive = True,
                            .CreatedBy = "System",
                            .CreatedDate = DateTime.Now
                        }
                        db.tr_weighbridge_log.Add(logEntry)
                        db.SaveChanges()

                        ' Clear form and show success
                        ClearTruckEntryForm()
                        ShowAlert($"Kendaraan {vehicleNumber} berhasil dicatat masuk dengan berat gross {transaction.GrossWeight:F2} kg", "success")
                        AddLogEntry("Kendaraan", $"Kendaraan masuk: {vehicleNumber} (ID: {transaction.Kendaraan_id}) - {transaction.GrossWeight:F2} kg")

                    Catch saveEx As Exception
                        Dim saveErrorMsg = "Database save error: " & saveEx.Message
                        If saveEx.InnerException IsNot Nothing Then
                            saveErrorMsg &= " Inner: " & saveEx.InnerException.Message
                        End If
                        AddLogEntry("Error", saveErrorMsg)
                        Throw New Exception("Failed to save weighbridge transaction", saveEx)
                    End Try

                Catch dbEx As Exception
                    Dim dbErrorMsg = "Database operation error: " & dbEx.Message
                    If dbEx.InnerException IsNot Nothing Then
                        dbErrorMsg &= " Inner: " & dbEx.InnerException.Message
                    End If
                    AddLogEntry("Error", dbErrorMsg)
                    Throw New Exception("Failed to complete truck entry", dbEx)
                End Try
            End Using

        Catch ex As Exception
            Dim errorMsg = "Error saving truck entry: " & ex.Message
            If ex.InnerException IsNot Nothing Then
                errorMsg &= " Inner Exception: " & ex.InnerException.Message
            End If
            ShowAlert(errorMsg, "danger")
            AddLogEntry("Error", "Failed to save truck entry: " & errorMsg)
            Throw
        End Try
    End Sub   
    Private Sub HandleSearchTruck(vehicleIdStr As String)
        Try
            Dim vehicleId As Integer
            If Not Integer.TryParse(vehicleIdStr, vehicleId) Then
                ShowAlert("ID kendaraan tidak valid", "warning")
                Return
            End If

            Using db As New BrightEntities()
                ' Get the vehicle by ID
                Dim vehicle = db.tm_kendaraan.FirstOrDefault(Function(k) k.Id = vehicleId)

                If vehicle Is Nothing Then
                    ShowAlert($"Kendaraan dengan ID {vehicleId} tidak ditemukan", "warning")
                    AddLogEntry("Search", $"Vehicle not found: ID {vehicleId}")
                    Return
                End If

                ' Find active transaction for this vehicle
                Dim activeTransaction = db.tr_po_receive_weighbridge.
                    FirstOrDefault(Function(t) t.Kendaraan_id = vehicle.Id AndAlso t.Status = "WEIGH_IN")

                If activeTransaction IsNot Nothing Then
                    ' Found active truck - show details via JavaScript
                    Dim script = $"
                        document.getElementById('truckStatus').innerHTML = '<strong style=""color: green;"">Truk Aktif Ditemukan</strong>';
                        document.getElementById('truckDetails').style.display = 'block';
                        document.getElementById('truckDetailContent').innerHTML = `
                            <p><strong>Kendaraan:</strong> {vehicle.NoPolisi} - {vehicle.NamaKendaraan}</p>
                            <p><strong>Driver:</strong> {activeTransaction.DriverName}</p>
                            <p><strong>Waktu Masuk:</strong> {activeTransaction.WeighInTime:dd/MM/yyyy HH:mm}</p>
                            <p><strong>Berat Gross:</strong> {activeTransaction.GrossWeight:F2} kg</p>
                            <p><strong>Keterangan:</strong> {activeTransaction.Keterangan}</p>
                        `;
                        document.getElementById('btnSaveTruckExit').disabled = false;
                    "
                    ClientScript.RegisterStartupScript(Me.GetType(), "ShowTruckDetails", script, True)
                    AddLogEntry("Search", $"Found active truck: {vehicle.NoPolisi}")
                Else
                    ' No active truck found
                    Dim script = $"
                        document.getElementById('truckStatus').innerHTML = '<strong style=""color: red;"">Truk Tidak Ditemukan atau Sudah Keluar</strong>';
                        document.getElementById('truckDetails').style.display = 'none';
                        document.getElementById('btnSaveTruckExit').disabled = true;
                    "
                    ClientScript.RegisterStartupScript(Me.GetType(), "HideTruckDetails", script, True)
                    ShowAlert($"Truk {vehicle.NoPolisi} tidak ditemukan atau sudah keluar", "warning")
                End If
            End Using

        Catch ex As Exception
            ShowAlert("Error searching truck: " & ex.Message, "danger")
        End Try
    End Sub  
    Private Sub HandleTruckExit(jsonData As String)
        Try
            Dim data = JsonConvert.DeserializeObject(Of Dictionary(Of String, Object))(jsonData)
            Dim vehicleId = Convert.ToInt32(data("vehicleId"))

            Using db As New BrightEntities()
                ' Get the vehicle by ID
                Dim vehicle = db.tm_kendaraan.FirstOrDefault(Function(k) k.Id = vehicleId)

                If vehicle Is Nothing Then
                    ShowAlert($"Kendaraan dengan ID {vehicleId} tidak ditemukan", "warning")
                    Return
                End If

                ' Find active transaction for this vehicle
                Dim transaction = db.tr_po_receive_weighbridge.
                    FirstOrDefault(Function(t) t.Kendaraan_id = vehicle.Id AndAlso t.Status = "WEIGH_IN")

                If transaction IsNot Nothing Then
                    ' Update transaction
                    transaction.TareWeight = Convert.ToDecimal(data("tareWeight"))
                    transaction.NetWeight = transaction.GrossWeight - transaction.TareWeight
                    transaction.WeighOutTime = DateTime.Now
                    transaction.Status = "COMPLETED"
                    transaction.ModifiedBy = "System"
                    transaction.ModifiedDate = DateTime.Now

                    If data.ContainsKey("remarks") AndAlso Not String.IsNullOrEmpty(data("remarks").ToString()) Then
                        transaction.Keterangan += " | " & data("remarks").ToString()
                    End If' Create exit log entry
                    Dim logEntry As New tr_weighbridge_log() With {
                        .PoReceiveWeighbridge_id = transaction.Id,
                        .WeighType = "TARE",
                        .Weight = transaction.TareWeight.Value,
                        .WeighTime = DateTime.Now,
                        .OperatorName = "System",
                        .TruckNumber = vehicle.NoPolisi,
                        .ScaleId = "SCALE01",
                        .RawData = jsonData,
                        .IsActive = True,
                        .CreatedBy = "System",
                        .CreatedDate = DateTime.Now
                    }

                    db.tr_weighbridge_log.Add(logEntry)
                    db.SaveChanges()                    ' Clear form and show success
                    ClearTruckExitForm()
                    ShowAlert($"Truk {vehicle.NoPolisi} berhasil keluar. Berat netto: {transaction.NetWeight:F2} kg", "success")
                    AddLogEntry("Truck", $"Truk keluar: {vehicle.NoPolisi} - Netto: {transaction.NetWeight:F2} kg")
                Else
                    ShowAlert($"Truk {vehicle.NoPolisi} tidak ditemukan atau sudah keluar", "warning")
                End If
            End Using

        Catch ex As Exception
            ShowAlert("Error saving truck exit: " & ex.Message, "danger")
        End Try
    End Sub

    Private Sub HandleRefreshHistory()
        Try
            Using db As New BrightEntities()
                ' Get today's transactions with vehicle information
                Dim today = DateTime.Today
                Dim transactions = db.tr_po_receive_weighbridge.
                    Include("tm_kendaraan").
                    Where(Function(t) t.Tanggal >= today).
                    OrderByDescending(Function(t) t.CreatedDate).
                    ToList()

                ' Build HTML table
                Dim html As New StringBuilder()
                For Each transaction In transactions
                    Dim statusColor = If(transaction.Status = "COMPLETED", "green", "orange")
                    Dim vehicleNumber = If(transaction.tm_kendaraan?.NoPolisi, "N/A")
                    html.AppendLine("<tr>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{vehicleNumber}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{transaction.DriverName}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{transaction.Keterangan}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{If(transaction.WeighInTime.HasValue, transaction.WeighInTime.Value.ToString("HH:mm"), "-")}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{If(transaction.WeighOutTime.HasValue, transaction.WeighOutTime.Value.ToString("HH:mm"), "-")}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{If(transaction.GrossWeight.HasValue, transaction.GrossWeight.Value.ToString("F2"), "-")}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{If(transaction.TareWeight.HasValue, transaction.TareWeight.Value.ToString("F2"), "-")}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd;'>{If(transaction.NetWeight.HasValue, transaction.NetWeight.Value.ToString("F2"), "-")}</td>")
                    html.AppendLine($"<td style='padding: 8px; border: 1px solid #ddd; color: {statusColor}; font-weight: bold;'>{transaction.Status}</td>")
                    html.AppendLine("</tr>")
                Next

                If transactions.Count = 0 Then
                    html.AppendLine("<tr><td colspan='9' style='padding: 20px; text-align: center; color: #666;'>Belum ada transaksi hari ini</td></tr>")
                End If

                ' Update table via JavaScript
                Dim script = $"document.getElementById('historyTableBody').innerHTML = `{html.ToString()}`;"
                ClientScript.RegisterStartupScript(Me.GetType(), "UpdateHistory", script, True)
            End Using

        Catch ex As Exception
            ShowAlert("Error loading history: " & ex.Message, "danger")
        End Try
    End Sub

    Private Sub HandleExportExcel()
        ' TODO: Implement Excel export
        ShowAlert("Export Excel akan diimplementasikan nanti", "info")
    End Sub
    Private Sub ClearTruckEntryForm()
        Dim script = "
            document.getElementById('" & VehicleDropdownEntry.ClientID & "').selectedIndex = 0;
            document.getElementById('" & DriverNameEntry.ClientID & "').value = '';
            document.getElementById('" & CompanyEntry.ClientID & "').value = '';
            document.getElementById('" & MaterialEntry.ClientID & "').value = '';
            document.getElementById('" & RemarksEntry.ClientID & "').value = '';
        "
        ClientScript.RegisterStartupScript(Me.GetType(), "ClearEntryForm", script, True)
    End Sub
    Private Sub ClearTruckExitForm()
        Dim script = "
            document.getElementById('" & VehicleDropdownExit.ClientID & "').selectedIndex = 0;
            document.getElementById('" & RemarksExit.ClientID & "').value = '';
            document.getElementById('truckStatus').innerHTML = 'Belum ada data';
            document.getElementById('truckDetails').style.display = 'none';
            document.getElementById('btnSaveTruckExit').disabled = true;
        "
        ClientScript.RegisterStartupScript(Me.GetType(), "ClearExitForm", script, True)
    End Sub

    Private Function GenerateReceiveNumber() As String
        Dim today = DateTime.Today
        Dim prefix = "WB" & today.ToString("yyyyMMdd")

        Using db As New BrightEntities()
            Dim lastNumber = db.tr_po_receive_weighbridge.
                Where(Function(t) t.NoReceive.StartsWith(prefix)).
                Count()

            Return prefix & (lastNumber + 1).ToString("D3")
        End Using
    End Function

    Private Sub LoadVehicles()
        Try
            Using context As New BrightEntities()
                Dim vehicles = context.tm_kendaraan.Where(Function(v) v.NoPolisi IsNot Nothing) _
                    .OrderBy(Function(v) v.NoPolisi) _
                    .Select(Function(v) New With {
                        .Id = v.Id,
                        .DisplayText = v.NoPolisi & " - " & If(v.NamaKendaraan, "") & " (" & If(v.NamaSopir, "No Driver") & ")",
                        .NoPolisi = v.NoPolisi,
                        .NamaKendaraan = v.NamaKendaraan,
                        .NamaSopir = v.NamaSopir
                    }).ToList()

                ' Bind to entry dropdown
                VehicleDropdownEntry.DataSource = vehicles
                VehicleDropdownEntry.DataBind()

                ' Bind to exit dropdown  
                VehicleDropdownExit.DataSource = vehicles
                VehicleDropdownExit.DataBind()

                ' Add data attributes for driver names
                For Each item As ListItem In VehicleDropdownEntry.Items
                    If item.Value <> "" Then
                        Dim vehicle = vehicles.FirstOrDefault(Function(v) v.Id.ToString() = item.Value)
                        If vehicle IsNot Nothing Then
                            item.Attributes("data-driver-name") = vehicle.NamaSopir
                        End If
                    End If
                Next

                For Each item As ListItem In VehicleDropdownExit.Items
                    If item.Value <> "" Then
                        Dim vehicle = vehicles.FirstOrDefault(Function(v) v.Id.ToString() = item.Value)
                        If vehicle IsNot Nothing Then
                            item.Attributes("data-driver-name") = vehicle.NamaSopir
                        End If
                    End If
                Next

            End Using
        Catch ex As Exception
            ShowAlert("Error loading vehicles: " & ex.Message, "danger")
            AddLogEntry("Error", "Failed to load vehicles: " & ex.Message)
        End Try
    End Sub

    Private Sub HandleSaveNewVehicle(jsonData As String)
        Try
            Dim vehicleData = JsonConvert.DeserializeObject(Of Dictionary(Of String, Object))(jsonData)
            
            Using context As New BrightEntities()
                ' Check if license plate already exists - use case-insensitive comparison that EF can handle
                Dim plateToCheck = vehicleData("noPolisi").ToString().ToUpper()
                Dim existingVehicle = context.tm_kendaraan.FirstOrDefault(Function(v) v.NoPolisi = plateToCheck)
                If existingVehicle IsNot Nothing Then
                    ShowAlert("No. Polisi sudah terdaftar!", "warning")
                    Return
                End If

                ' Create new vehicle
                Dim newVehicle As New tm_kendaraan With {
                    .NoPolisi = vehicleData("noPolisi").ToString().ToUpper(),
                    .NamaKendaraan = If(vehicleData.ContainsKey("namaKendaraan"), vehicleData("namaKendaraan").ToString(), ""),
                    .Jenis = If(vehicleData.ContainsKey("jenis"), vehicleData("jenis").ToString(), ""),
                    .NamaSopir = If(vehicleData.ContainsKey("namaSopir"), vehicleData("namaSopir").ToString(), ""),
                    .NoHp = If(vehicleData.ContainsKey("noHp"), vehicleData("noHp").ToString(), ""),
                    .CreatedBy = "WeighbridgeSystem",
                    .CreatedDate = DateTime.Now
                }

                context.tm_kendaraan.Add(newVehicle)
                context.SaveChanges()

                ' Reload vehicles
                LoadVehicles()

                ' Select the new vehicle
                VehicleDropdownEntry.SelectedValue = newVehicle.Id.ToString()
                DriverNameEntry.Text = newVehicle.NamaSopir

                ShowAlert("Kendaraan berhasil ditambahkan: " & newVehicle.NoPolisi, "success")
                AddLogEntry("User", "Added new vehicle: " & newVehicle.NoPolisi)

                ' Close modal via JavaScript
                Page.ClientScript.RegisterStartupScript(Me.GetType(), "CloseModal", "$('#addVehicleModal').modal('hide'); $('#addVehicleForm')[0].reset();", True)
            End Using

        Catch ex As Exception
            ShowAlert("Error saving new vehicle: " & ex.Message, "danger")
            AddLogEntry("Error", "Failed to save new vehicle: " & ex.Message)
        End Try
    End Sub
End Class

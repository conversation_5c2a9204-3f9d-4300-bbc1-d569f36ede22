﻿Imports DevExpress.Web
Imports Bright.Controller
Imports Bright.MVVM
Public Class frmWeighbridgeConfig
    Inherits PageBase

    <Create(Scope:=CreateScope.Session)>
    Property Controller As tm_weighbridge_configController

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_list").ClientVisible = (Controller.SelectedItem Is Nothing)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = (Controller.SelectedItem IsNot Nothing)
    End Sub

    <EventSubscription>
    Public Sub OnMsgChanged(sender As Object, e As EventArgs)
        ltl_msg.Text = Controller.sMsg
    End Sub

    Public Sub New()
        MyBase.New("50301") ' Menu code for Weighbridge Config

    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            'ucWeighbridgeConfig_list.Controller = Controller
            'ucWeighbridgeConfig_edit.Controller = Controller
            Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = False
        End If
    End Sub

    Private Sub ASPxCallbackPanel1_Callback(sender As Object, e As CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If
        Dim s() As String = e.Parameter.ToString.ToLower.Split(";")
        Select Case s(0)
            Case "new"
                Controller.AddNewItem()
            Case "reset"
                Controller.Reset()
            Case "save"
                Me.ucWeighbridgeConfig_edit.SavingPrepare()
                Controller.Saving()
                If Controller.Saved Then
                    Controller.Reset()
                Else
                    Me.OnEditChanged(Nothing, Nothing)
                End If
            Case "btn_edit"
                Controller.Action = Action.Edit
                Controller.SelectItem(s(1))
            Case "btn_delete"
                Controller.Action = Action.Delete
                Controller.DeleteItem(s(1))
            Case "test_connection"
                If s.Length > 1 Then
                    Dim configId As Integer = CInt(s(1))
                    Dim result As Boolean = Controller.TestConnection(configId)
                    ltl_msg.Text = If(result, "Koneksi berhasil!", "Koneksi gagal!")
                End If
        End Select
    End Sub


End Class
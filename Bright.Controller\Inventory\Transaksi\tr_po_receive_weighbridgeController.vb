Imports Bright.Controller
Imports Bright.Domain
Imports Bright.Helpers
Imports Bright.Helpers.WeighBridge
Imports Z.EntityFramework.Plus
Imports System.Transactions


Public Class tr_po_receive_weighbridgeController
    Inherits Controller(Of tr_po_receive_weighbridge)
    Implements Itr_po_receive_weighbridgeListController, Itr_po_receive_weighbridgeEditController

    Private _selectedPrint As DevExpress.XtraReports.UI.XtraReport
    Property SelectedPrint As DevExpress.XtraReports.UI.XtraReport
        Get
            Return _selectedPrint
        End Get
        Set(value As DevExpress.XtraReports.UI.XtraReport)
            _selectedPrint = value
            RaiseEvent OnPrinting(Nothing, Nothing)
        End Set
    End Property
    Public Event OnPrinting As EventHandler

    Sub SelectPrint(id As String)
        'Dim cls As New clsRPT
        'SelectedPrint = cls.GetRpt_POReceiveWeighbridge(id)
    End Sub

#Region "List Controller Implementation"

    Public ReadOnly Property DataList(bPosted As <PERSON><PERSON><PERSON>, sStatus As String, StartDate As Date, endDate As Date) As IQueryable(Of tr_po_receive_weighbridge) Implements Itr_po_receive_weighbridgeListController.DataList
        Get
            Dim uow = ContextFactory.Instance.GetContextPerRequest
            Dim os = uow.tr_po_receive_weighbridge.AsNoTracking()

            ' Filter berdasarkan cabang jika bukan head office
            If AuthHelper.GetLoggedInUserInfo.tm_cabang.IsHeadOffice <> True Then
                Dim cab_id As Integer = AuthHelper.GetLoggedInUserInfo.Cabang_id
                os = os.Where(Function(f) f.Cabang_id = cab_id)
            End If

            os = os.Where(Function(f) f.Posted = bPosted AndAlso f.Status = sStatus)
            If sStatus = "Completed" Then
                os = os.Where(Function(f) f.Tanggal >= StartDate AndAlso f.Tanggal <= endDate)
            End If

            Return os.OrderByDescending(Function(f) f.Tanggal).ThenByDescending(Function(f) f.Id)
        End Get
    End Property

#End Region

#Region "Edit Controller Implementation"

    Public ReadOnly Property AreaList As IQueryable(Of tm_area) Implements Itr_po_receive_weighbridgeEditController.AreaList
        Get
            Dim uow = ContextFactory.Instance.GetContextPerRequest
            Return uow.tm_area.AsNoTracking().Where(Function(f) f.Aktif = True).OrderBy(Function(f) f.NamaArea)
        End Get
    End Property

    Public ReadOnly Property CabangList As IQueryable(Of tm_cabang) Implements Itr_po_receive_weighbridgeEditController.CabangList
        Get
            Dim uow = ContextFactory.Instance.GetContextPerRequest
            Dim os = uow.tm_cabang.AsNoTracking().Where(Function(f) f.Aktif = True)

            ' Filter berdasarkan cabang jika bukan head office
            If AuthHelper.GetLoggedInUserInfo.tm_cabang.IsHeadOffice <> True Then
                Dim cab_id As Integer = AuthHelper.GetLoggedInUserInfo.Cabang_id
                os = os.Where(Function(f) f.Id = cab_id)
            End If

            Return os.OrderBy(Function(f) f.NamaCabang)
        End Get
    End Property

    Public ReadOnly Property LokasiList As IQueryable(Of tm_lokasi) Implements Itr_po_receive_weighbridgeEditController.LokasiList
        Get
            Dim uow = ContextFactory.Instance.GetContextPerRequest
            Return uow.tm_lokasi.AsNoTracking().Where(Function(f) f.Aktif = True).OrderBy(Function(f) f.NamaLokasi)
        End Get
    End Property

    Public ReadOnly Property POList As IQueryable(Of tr_po) Implements Itr_po_receive_weighbridgeEditController.POList
        Get
            Dim uow = ContextFactory.Instance.GetContextPerRequest
            Dim os = uow.tr_po.AsNoTracking()

            ' Filter berdasarkan cabang dan status
            If AuthHelper.GetLoggedInUserInfo.tm_cabang.IsHeadOffice <> True Then
                Dim cab_id As Integer = AuthHelper.GetLoggedInUserInfo.Cabang_id
                os = os.Where(Function(f) f.Cabang_id = cab_id)
            End If

            ' Only show approved POs that are not fully received
            Return os.Where(Function(p) p.Status = "APPROVED" AndAlso
                               p.tr_po_line.Any(Function(l) l.QtyReceived < l.QtyOrdered)) _
                       .OrderByDescending(Function(f) f.Tanggal)
        End Get
    End Property

    Public ReadOnly Property KendaraanList As IQueryable(Of tm_kendaraan) Implements Itr_po_receive_weighbridgeEditController.KendaraanList
        Get
            Dim uow = ContextFactory.Instance.GetContextPerRequest
            Return uow.tm_kendaraan.AsNoTracking() _
                .Where(Function(k) k.Aktif = True) _
                .OrderBy(Function(k) k.NoPolisi)
        End Get
    End Property

    Public ReadOnly Property StatusList As List(Of String) Implements Itr_po_receive_weighbridgeEditController.StatusList
        Get
            Return New List(Of String) From {"DRAFT", "WEIGH_IN", "COMPLETED", "POSTED", "CANCELLED"}
        End Get
    End Property

    Public ReadOnly Property QualityStatusList As List(Of String) Implements Itr_po_receive_weighbridgeEditController.QualityStatusList
        Get
            Return New List(Of String) From {"PASSED", "FAILED", "PENDING", "CONDITIONAL"}
        End Get
    End Property

    Public Function POLines(poId As Integer) As IQueryable(Of tr_po_line) Implements Itr_po_receive_weighbridgeEditController.POLines
        Dim uow = ContextFactory.Instance.GetContextPerRequest
        Return uow.tr_po_line.AsNoTracking() _
            .Where(Function(l) l.PO_id = poId) _
            .OrderBy(Function(l) l.Id)
    End Function

#End Region

#Region "Weighbridge Integration Methods"

    ''' <summary>
    ''' Start weighing process with real-time weight capture
    ''' </summary>
    Public Function StartWeighIn(receiveId As Integer, truckNumber As String, driverName As String, Optional driverPhone As String = "") As (Success As Boolean, Message As String) Implements Itr_po_receive_weighbridgeEditController.StartWeighIn
        Try
            Using scope As New TransactionScope()
                Dim uow = ContextFactory.Instance.GetContextPerRequest
                Dim receive = uow.tr_po_receive_weighbridge.Include("tm_kendaraan").FirstOrDefault(Function(r) r.Id = receiveId)

                If receive Is Nothing Then
                    Return (False, "Data receive tidak ditemukan")
                End If ' Validate truck info
                If String.IsNullOrWhiteSpace(truckNumber) OrElse String.IsNullOrWhiteSpace(driverName) Then
                    Return (False, "Nomor truck dan nama driver harus diisi")
                End If

                ' Find or create vehicle record
                Dim vehicle = uow.tm_kendaraan.FirstOrDefault(Function(k) k.NoPolisi = truckNumber.Trim().ToUpper())
                If vehicle Is Nothing Then
                    ' Create new vehicle record
                    vehicle = New tm_kendaraan() With {
                        .NoPolisi = truckNumber.Trim().ToUpper(),
                        .NamaKendaraan = "Auto-created from weighbridge",
                        .Jenis = "Truck",
                        .NamaSopir = driverName.Trim(),
                        .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName,
                        .CreatedDate = DateTime.Now
                    }
                    uow.tm_kendaraan.Add(vehicle)
                    uow.SaveChanges()
                End If

                ' Get weighbridge service
                Dim manager = WeighBridgeManager.Instance
                Dim service = manager.GetService(receive.Cabang_id)

                If service Is Nothing Then
                    Return (False, "Weighbridge tidak tersedia untuk cabang ini")
                End If

                ' Get current weight
                Dim weightData = service.GetWeight()
                If Not weightData.IsStable Then
                    Return (False, "Berat tidak stabil. Tunggu sampai stabil sebelum melanjutkan.")
                End If                ' Update receive data
                receive.Kendaraan_id = vehicle.Id
                receive.DriverName = driverName.Trim()
                receive.DriverPhone = driverPhone.Trim()
                receive.StartWeighIn(weightData.Weight, AuthHelper.GetLoggedInUserInfo.UserName)

                ' Create weighbridge log
                Dim log As New tr_weighbridge_log With {
                        .PoReceiveWeighbridge_id = receive.Id,
                        .WeighType = "GROSS",
                        .Weight = weightData.Weight,
                        .WeighTime = DateTime.Now,
                        .OperatorName = AuthHelper.GetLoggedInUserInfo.UserName,
                        .TruckNumber = truckNumber.Trim().ToUpper(),
                        .ScaleId = weightData.ScaleId,
                        .RawData = weightData.RawData,
                        .IsActive = True,
                        .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName,
                        .CreatedDate = DateTime.Now
                    }

                uow.tr_weighbridge_log.Add(log)
                uow.SaveChanges()
                scope.Complete()

                Return (True, $"Weigh-in berhasil. Gross weight: {weightData.Weight} {weightData.Unit}")
            End Using

        Catch ex As Exception
            Return (False, $"Error during weigh-in: {ex.Message}")
        End Try
    End Function

    ''' <summary>
    ''' Complete weighing process with tare weight
    ''' </summary>
    Public Function CompleteWeighOut(receiveId As Integer) As (Success As Boolean, Message As String) Implements Itr_po_receive_weighbridgeEditController.CompleteWeighOut
        Try
            Using scope As New TransactionScope()
                Dim uow = ContextFactory.Instance.GetContextPerRequest
                Dim receive = uow.tr_po_receive_weighbridge.Include("tm_kendaraan").FirstOrDefault(Function(r) r.Id = receiveId)

                If receive Is Nothing Then
                    Return (False, "Data receive tidak ditemukan")
                End If

                If receive.Status <> "WEIGH_IN" Then
                    Return (False, "Transaksi tidak dalam status weigh-in")
                End If

                ' Get weighbridge service
                Dim manager = WeighBridgeManager.Instance
                Dim service = manager.GetService(receive.Cabang_id)

                Dim weightData = service.GetWeight()
                If Not weightData.IsStable Then
                    Return (False, "Berat tidak stabil. Tunggu sampai stabil sebelum melanjutkan.")
                End If

                ' Complete weigh-out
                receive.CompleteWeighOut(weightData.Weight, AuthHelper.GetLoggedInUserInfo.UserName)

                ' Validate weights
                Dim validation = receive.ValidateWeights()
                If Not validation.IsValid Then
                    Return (False, validation.Message)
                End If                ' Create weighbridge log for tare
                Dim log As New tr_weighbridge_log With {
                        .PoReceiveWeighbridge_id = receive.Id,
                        .WeighType = "TARE",
                        .Weight = weightData.Weight,
                        .WeighTime = DateTime.Now,
                        .OperatorName = AuthHelper.GetLoggedInUserInfo.UserName,
                        .TruckNumber = If(receive.tm_kendaraan?.NoPolisi, "Unknown"),
                        .ScaleId = weightData.ScaleId,
                        .RawData = weightData.RawData,
                        .IsActive = True,
                        .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName,
                        .CreatedDate = DateTime.Now
                    }

                uow.tr_weighbridge_log.Add(log)
                uow.SaveChanges()
                scope.Complete()

                Return (True, $"Weigh-out berhasil. Net weight: {receive.CalculatedNetWeight} kg")
            End Using

        Catch ex As Exception
            Return (False, $"Error during weigh-out: {ex.Message}")
        End Try
    End Function

    ''' <summary>
    ''' Get real-time weight from weighbridge
    ''' </summary>
    Public Function GetCurrentWeight(cabangId As Integer) As (Success As Boolean, WeightData As WeightData, Message As String) Implements Itr_po_receive_weighbridgeEditController.GetCurrentWeight
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim service = manager.GetService(cabangId)

            If service Is Nothing Then
                Return (False, Nothing, "Weighbridge tidak tersedia untuk cabang ini")
            End If

            If Not service.IsConnected() Then
                Return (False, Nothing, "Weighbridge tidak terhubung")
            End If

            Dim weightData = service.GetWeight()
            Return (True, weightData, "Success")

        Catch ex As Exception
            Return (False, Nothing, $"Error getting weight: {ex.Message}")
        End Try
    End Function

#End Region

#Region "Business Logic Methods"

    ''' <summary>
    ''' Create new receive weighbridge from PO
    ''' </summary>
    Public Function CreateFromPO(poId As Integer, cabangId As Integer, lokasiId As Integer) As (Success As Boolean, ReceiveId As Integer, Message As String) Implements Itr_po_receive_weighbridgeEditController.CreateFromPO
        Try

            Using scope As New TransactionScope()
                Dim uow = ContextFactory.Instance.GetContextPerRequest
                Dim po = uow.tr_po.Include("tr_po_line.tm_item_master").FirstOrDefault(Function(p) p.Id = poId)

                If po Is Nothing Then
                    Return (False, 0, "Purchase Order tidak ditemukan")
                End If

                If po.Status <> "APPROVED" Then
                    Return (False, 0, "Purchase Order belum approved")
                End If

                ' Check if there are items to receive
                Dim itemsToReceive = po.tr_po_line.Where(Function(l) l.QtyReceived < l.QtyOrdered).ToList()
                If Not itemsToReceive.Any() Then
                    Return (False, 0, "Semua item PO sudah diterima lengkap")
                End If                ' Create receive header
                Dim receive As New tr_po_receive_weighbridge With {
                        .Area_id = po.Area_id,
                        .Cabang_id = cabangId,
                        .Lokasi_id = lokasiId,
                        .Tanggal = DateTime.Now.Date,
                        .Status = "DRAFT",
                        .Posted = False,
                        .Keterangan = $"PO: {po.NoPO} - Created from Purchase Order",
                        .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName,
                        .CreatedDate = DateTime.Now
                    }

                ' Generate receive number
                receive.GenerateReceiveNumber(uow)

                uow.tr_po_receive_weighbridge.Add(receive)
                uow.SaveChanges() ' Save to get ID

                ' Create receive lines for remaining quantities
                For Each poLine In itemsToReceive
                    Dim remainingQty = poLine.QtyOrdered - poLine.QtyReceived
                    If remainingQty > 0 Then
                        Dim receiveLine As New tr_po_receive_weighbridge_line With {
                                .PoReceiveWeighbridge_id = receive.Id,
                                .PoLine_id = poLine.Id,
                                .QtyReceived = remainingQty, ' Default to remaining quantity
                                .QualityStatus = "PENDING",
                                .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName,
                                .CreatedDate = DateTime.Now
                            }

                        uow.tr_po_receive_weighbridge_line.Add(receiveLine)
                    End If
                Next

                uow.SaveChanges()
                scope.Complete()

                Return (True, receive.Id, $"Receive weighbridge berhasil dibuat: {receive.NoReceive}")
            End Using

        Catch ex As Exception
            Return (False, 0, $"Error creating receive: {ex.Message}")
        End Try
    End Function

    ''' <summary>
    ''' Post transaction to inventory and create journal
    ''' </summary>
    Public Function PostTransaction(receiveId As Integer) As (Success As Boolean, Message As String) Implements Itr_po_receive_weighbridgeEditController.PostTransaction
        Try
            Using scope As New TransactionScope()
                Dim uow = ContextFactory.Instance.GetContextPerRequest
                Dim receive = uow.tr_po_receive_weighbridge.Include("tr_po_receive_weighbridge_line.tr_po_line.tm_item_master").FirstOrDefault(Function(r) r.Id = receiveId)

                If receive Is Nothing Then
                    Return (False, "Data receive tidak ditemukan")
                End If

                ' Validate for posting
                Dim validation = receive.ValidateForPosting()
                If Not validation.IsValid Then
                    Return (False, String.Join("; ", validation.Messages))
                End If

                ' Create inventory movements
                For Each line In receive.tr_po_receive_weighbridge_line
                    Dim stockMove As New tm_inv_stockmove With {
                            .Tanggal = receive.Tanggal,
                            .ItemMaster_id = line.tr_po_line.Item_id,
                            .Lokasi_id = receive.Lokasi_id,
                            .TypeTrans = "IN",
                            .Qty = line.QtyReceived,
                            .Price = line.tr_po_line.HargaBeli,
                            .StandartCost = line.tr_po_line.HargaBeli,
                            .PoReceiveWeighbridgeLine_id = line.Id,
                            .BatchNumber = line.BatchNumber,
                            .ExpiredDate = line.ExpiredDate
                        }

                    uow.tm_inv_stockmove.Add(stockMove)

                    ' Update PO line received quantity
                    line.tr_po_line.QtyReceived += line.QtyReceived
                Next line

                ' Update receive status
                receive.Posted = True
                receive.PostedBy = AuthHelper.GetLoggedInUserInfo.UserName
                receive.PostedDate = DateTime.Now

                ' TODO: Create financial journal entries using JournalService
                ' This would integrate with the accounting system

                uow.SaveChanges()
                scope.Complete()

                Return (True, "Transaksi berhasil di-posting")
            End Using

        Catch ex As Exception
            Return (False, $"Error posting transaction: {ex.Message}")
        End Try
    End Function

#End Region

#Region "Required CRUD Methods"

    Public Overrides Sub AddNewItem()
        Dim userInfo = AuthHelper.GetLoggedInUserInfo()
        Action = Action.AddNew
        Dim o = New tr_po_receive_weighbridge With {.Area_id = userInfo.Area_id,
                .Cabang_id = userInfo.Cabang_id,
                .Tanggal = Now,
                .Status = "New",
                .Posted = False
            }

        SelectedItem = o
    End Sub

    Public Overrides Sub SelectItem(id As String)
        Dim uow = ContextFactory.Instance.GetContextPerRequest
        SelectedItem = uow.tr_po_receive_weighbridge.AsNoTracking().FirstOrDefault(Function(f) f.Id = CInt(id))
        Action = Action.Edit
    End Sub

    Public Overrides Sub DeleteItem(id As String)
        Using uow As New BrightEntities
            uow.Configuration.AutoDetectChangesEnabled = False
            uow.Configuration.ValidateOnSaveEnabled = False
            Try
                Dim receive = uow.tr_po_receive_weighbridge.FirstOrDefault(Function(r) r.Id = CInt(id))
                If receive IsNot Nothing Then
                    ' Check if can be deleted
                    If receive.Posted Then
                        sMsg = "Transaksi yang sudah diposting tidak dapat dihapus"
                        Saved = False
                        Return
                    End If

                    If receive.Status = "COMPLETED" Then
                        sMsg = "Transaksi yang sudah completed tidak dapat dihapus"
                        Saved = False
                        Return
                    End If

                    ' Delete related records first
                    uow.tr_weighbridge_log.RemoveRange(uow.tr_weighbridge_log.Where(Function(l) l.PoReceiveWeighbridge_id = CInt(id)))
                    uow.tr_po_receive_weighbridge_line.RemoveRange(uow.tr_po_receive_weighbridge_line.Where(Function(l) l.PoReceiveWeighbridge_id = CInt(id)))
                    uow.tr_po_receive_weighbridge.Remove(receive)

                    uow.SaveChanges()
                    Reset()
                    sMsg = "Data berhasil dihapus"
                    Saved = True
                Else
                    sMsg = "Data tidak ditemukan"
                    Saved = False
                End If
            Catch ex As Exception
                sMsg = ExceptionManager.DoLogAndGetFriendlyMessageForException(ex)
                Saved = False
            Finally
                uow.Configuration.AutoDetectChangesEnabled = True
                uow.Configuration.ValidateOnSaveEnabled = True
            End Try
        End Using
    End Sub

#End Region

#Region "Override Methods"

    Public Overrides Sub Saving()
        Try
            Using scope As New TransactionScope()
                Dim uow = ContextFactory.Instance.GetContextPerRequest

                If SelectedItem IsNot Nothing Then
                    ' Validate before saving
                    Dim driverValidation = SelectedItem.ValidateDriverInfo()
                    If Not driverValidation.IsValid Then
                        sMsg = driverValidation.Message
                        Saved = False
                        Return
                    End If

                    Dim timingValidation = SelectedItem.ValidateTiming()
                    If Not timingValidation.IsValid Then
                        sMsg = timingValidation.Message
                        Saved = False
                        Return
                    End If

                    ' Generate receive number if new
                    If SelectedItem.Id = 0 Then
                        SelectedItem.GenerateReceiveNumber(uow)
                        SelectedItem.CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                        SelectedItem.CreatedDate = DateTime.Now
                    Else
                        SelectedItem.ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                        SelectedItem.ModifiedDate = DateTime.Now
                    End If

                    ' Save changes
                    If Action = Action.AddNew Then
                        uow.tr_po_receive_weighbridge.Add(SelectedItem)
                    Else
                        uow.Entry(SelectedItem).State = Entity.EntityState.Modified
                    End If

                    uow.SaveChanges()
                    scope.Complete()
                    sMsg = "Data berhasil disimpan"
                    Saved = True
                End If
            End Using

        Catch ex As Exception
            sMsg = $"Error saving: {ex.Message}"
            Saved = False
        End Try
    End Sub

#End Region

End Class

#Region "Controller Interfaces"

Public Interface Itr_po_receive_weighbridgeListController
    Inherits IControllerMain, IControllerList
    ReadOnly Property DataList(bPosted As Boolean, sStatus As String, StartDate As Date, endDate As Date) As IQueryable(Of tr_po_receive_weighbridge)
End Interface

Public Interface Itr_po_receive_weighbridgeEditController
    Inherits IControllerMain, IControllerEdit(Of tr_po_receive_weighbridge)
    ReadOnly Property AreaList As IQueryable(Of tm_area)
    ReadOnly Property CabangList As IQueryable(Of tm_cabang)
    ReadOnly Property LokasiList As IQueryable(Of tm_lokasi)
    ReadOnly Property POList As IQueryable(Of tr_po)
    ReadOnly Property KendaraanList As IQueryable(Of tm_kendaraan)
    ReadOnly Property StatusList As List(Of String)
    ReadOnly Property QualityStatusList As List(Of String)
    Function StartWeighIn(receiveId As Integer, truckNumber As String, driverName As String, Optional driverPhone As String = "") As (Success As Boolean, Message As String)
    Function CompleteWeighOut(receiveId As Integer) As (Success As Boolean, Message As String)
    Function GetCurrentWeight(cabangId As Integer) As (Success As Boolean, WeightData As WeightData, Message As String)
    Function CreateFromPO(poId As Integer, cabangId As Integer, lokasiId As Integer) As (Success As Boolean, ReceiveId As Integer, Message As String)
    Function PostTransaction(receiveId As Integer) As (Success As Boolean, Message As String)
    Function POLines(poId As Integer) As IQueryable(Of tr_po_line)
End Interface

#End Region

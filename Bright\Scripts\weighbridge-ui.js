/**
 * MODERN WEIGHBRIDGE USER INTERFACE
 * User-friendly interaction handler
 */

class WeighbridgeUI {
    constructor() {
        this.currentWeight = 0;
        this.isStable = false;
        this.weighingStep = '';
        this.isAutoMode = true;
        this.processData = {
            grossWeight: null,
            tareWeight: null,
            netWeight: null,
            truckNumber: '',
            driverName: '',
            poNumber: ''
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeDisplay();
        this.startWeightMonitoring();
    }
    
    /**
     * Setup all event listeners
     */
    setupEventListeners() {
        // Weight action buttons
        document.getElementById('btn-weigh-in')?.addEventListener('click', () => this.startWeighIn());
        document.getElementById('btn-weigh-out')?.addEventListener('click', () => this.startWeighOut());
        document.getElementById('btn-add-manual')?.addEventListener('click', () => this.showManualDialog());
        document.getElementById('btn-reset')?.addEventListener('click', () => this.resetProcess());
        
        // Mode toggle
        document.getElementById('toggle-auto-mode')?.addEventListener('change', (e) => {
            this.isAutoMode = e.target.checked;
            this.updateModeDisplay();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }
    
    /**
     * Handle keyboard shortcuts for faster operation
     */
    handleKeyboardShortcuts(event) {
        if (event.ctrlKey) {
            switch(event.key) {
                case '1':
                    event.preventDefault();
                    this.startWeighIn();
                    break;
                case '2':
                    event.preventDefault();
                    this.startWeighOut();
                    break;
                case 'm':
                    event.preventDefault();
                    this.showManualDialog();
                    break;
                case 'r':
                    event.preventDefault();
                    this.resetProcess();
                    break;
            }
        }
    }
    
    /**
     * Initialize the display elements
     */
    initializeDisplay() {
        this.updateWeightDisplay(0, 'kg', false);
        this.updateProcessFlow('idle');
        this.updateStatusCards();
        this.showWelcomeMessage();
    }
    
    /**
     * Start weight monitoring
     */
    startWeightMonitoring() {
        // Real-time weight updates every 500ms
        setInterval(() => {
            this.fetchCurrentWeight();
        }, 500);
    }
    
    /**
     * Fetch current weight from scale
     */
    async fetchCurrentWeight() {
        try {
            const response = await fetch('/api/weighbridge/current-weight');
            const data = await response.json();
            
            if (data.success) {
                this.currentWeight = data.weight;
                this.isStable = data.isStable;
                this.updateWeightDisplay(data.weight, data.unit, data.isStable);
                
                // Auto-capture stable weight in auto mode
                if (this.isAutoMode && data.isStable && this.weighingStep) {
                    this.autoCapture();
                }
            }
        } catch (error) {
            console.error('Error fetching weight:', error);
            this.showError('Koneksi ke timbangan terputus');
        }
    }
    
    /**
     * Update weight display
     */
    updateWeightDisplay(weight, unit = 'kg', isStable = false) {
        const weightValue = document.querySelector('.weight-value');
        const weightUnit = document.querySelector('.weight-unit');
        const stabilityIndicator = document.querySelector('.weight-stability');
        
        if (weightValue) {
            weightValue.textContent = weight.toLocaleString('id-ID', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        if (weightUnit) {
            weightUnit.textContent = unit;
        }
        
        if (stabilityIndicator) {
            stabilityIndicator.className = `weight-stability ${isStable ? 'weight-stable' : 'weight-unstable'}`;
        }
        
        // Update status text
        const statusText = document.querySelector('.weight-status');
        if (statusText) {
            statusText.textContent = isStable ? 'Berat Stabil' : 'Berat Tidak Stabil';
        }
    }
    
    /**
     * Start weigh-in process
     */
    async startWeighIn() {
        if (!this.validatePreWeighIn()) return;
        
        this.weighingStep = 'weigh_in';
        this.updateProcessFlow('weigh_in');
        this.showInstructions('Pastikan kendaraan berada di atas timbangan. Tunggu hingga berat stabil.');
        
        // Enable relevant buttons
        this.updateButtonStates(['btn-weigh-out', 'btn-add-manual'], false);
        this.updateButtonStates(['btn-reset'], true);
        
        if (!this.isAutoMode) {
            this.showCapturePrompt('weigh_in');
        }
    }
    
    /**
     * Start weigh-out process
     */
    async startWeighOut() {
        if (!this.processData.grossWeight) {
            this.showError('Timbang masuk harus dilakukan terlebih dahulu!');
            return;
        }
        
        this.weighingStep = 'weigh_out';
        this.updateProcessFlow('weigh_out');
        this.showInstructions('Pastikan kendaraan kosong berada di atas timbangan. Tunggu hingga berat stabil.');
        
        if (!this.isAutoMode) {
            this.showCapturePrompt('weigh_out');
        }
    }
    
    /**
     * Auto-capture stable weight
     */
    autoCapture() {
        if (!this.isStable || this.currentWeight <= 0) return;
        
        // Add delay to ensure stability
        setTimeout(() => {
            if (this.isStable) { // Double-check stability
                this.captureWeight(this.currentWeight);
            }
        }, 2000);
    }
    
    /**
     * Capture weight manually or automatically
     */
    async captureWeight(weight) {
        try {
            if (this.weighingStep === 'weigh_in') {
                await this.processWeighIn(weight);
            } else if (this.weighingStep === 'weigh_out') {
                await this.processWeighOut(weight);
            }
        } catch (error) {
            this.showError('Error memproses data: ' + error.message);
        }
    }
    
    /**
     * Process weigh-in
     */
    async processWeighIn(weight) {
        this.processData.grossWeight = weight;
        
        // Show success feedback
        this.showSuccess(`Timbang Masuk: ${weight.toLocaleString('id-ID')} kg`);
        
        // Update display
        this.updateStatusCards();
        this.updateProcessFlow('weigh_in_complete');
        
        // Enable weigh-out
        this.updateButtonStates(['btn-weigh-out'], true);
        this.weighingStep = '';
        
        // Auto-instruction for next step
        setTimeout(() => {
            this.showInstructions('Silakan lakukan timbang keluar setelah kendaraan kosong.');
        }, 3000);
    }
    
    /**
     * Process weigh-out
     */
    async processWeighOut(weight) {
        this.processData.tareWeight = weight;
        this.processData.netWeight = this.processData.grossWeight - weight;
        
        if (this.processData.netWeight <= 0) {
            this.showError('Net weight tidak valid. Periksa kembali proses penimbangan.');
            return;
        }
        
        // Show success feedback
        this.showSuccess(`Net Weight: ${this.processData.netWeight.toLocaleString('id-ID')} kg`);
        
        // Update display
        this.updateStatusCards();
        this.updateProcessFlow('complete');
        
        // Show distribution dialog
        setTimeout(() => {
            this.showDistributionDialog();
        }, 2000);
        
        this.weighingStep = '';
    }
    
    /**
     * Show distribution dialog
     */
    showDistributionDialog() {
        const dialog = document.getElementById('distribution-dialog');
        if (dialog) {
            // Update dialog with current data
            document.getElementById('dist-net-weight').textContent = 
                this.processData.netWeight.toLocaleString('id-ID') + ' kg';
            
            dialog.style.display = 'flex';
        }
    }
    
    /**
     * Update process flow indicator
     */
    updateProcessFlow(step) {
        const steps = document.querySelectorAll('.flow-step');
        
        steps.forEach(stepEl => {
            stepEl.classList.remove('active', 'completed');
        });
        
        switch(step) {
            case 'weigh_in':
                document.querySelector('[data-step="weigh_in"]')?.classList.add('active');
                break;
            case 'weigh_in_complete':
                document.querySelector('[data-step="weigh_in"]')?.classList.add('completed');
                break;
            case 'weigh_out':
                document.querySelector('[data-step="weigh_in"]')?.classList.add('completed');
                document.querySelector('[data-step="weigh_out"]')?.classList.add('active');
                break;
            case 'complete':
                document.querySelector('[data-step="weigh_in"]')?.classList.add('completed');
                document.querySelector('[data-step="weigh_out"]')?.classList.add('completed');
                document.querySelector('[data-step="distribute"]')?.classList.add('active');
                break;
        }
    }
    
    /**
     * Update status cards
     */
    updateStatusCards() {
        const grossCard = document.querySelector('.card-gross .card-value');
        const tareCard = document.querySelector('.card-tare .card-value');
        const netCard = document.querySelector('.card-net .card-value');
        
        if (grossCard) {
            grossCard.textContent = this.processData.grossWeight ? 
                this.processData.grossWeight.toLocaleString('id-ID') + ' kg' : '-';
        }
        
        if (tareCard) {
            tareCard.textContent = this.processData.tareWeight ? 
                this.processData.tareWeight.toLocaleString('id-ID') + ' kg' : '-';
        }
        
        if (netCard) {
            netCard.textContent = this.processData.netWeight ? 
                this.processData.netWeight.toLocaleString('id-ID') + ' kg' : '-';
        }
    }
    
    /**
     * Show success message
     */
    showSuccess(message) {
        this.showFeedback(message, 'success', '✓');
    }
    
    /**
     * Show error message
     */
    showError(message) {
        this.showFeedback(message, 'error', '✗');
    }
    
    /**
     * Show feedback overlay
     */
    showFeedback(message, type, icon) {
        const overlay = document.createElement('div');
        overlay.className = 'feedback-overlay';
        
        overlay.innerHTML = `
            <div class="feedback-content feedback-${type}">
                <div class="feedback-icon">${icon}</div>
                <h3>${message}</h3>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            overlay.remove();
        }, 3000);
        
        // Click to dismiss
        overlay.addEventListener('click', () => overlay.remove());
    }
    
    /**
     * Show instructions
     */
    showInstructions(message) {
        const instructionEl = document.getElementById('current-instruction');
        if (instructionEl) {
            instructionEl.textContent = message;
            instructionEl.style.display = 'block';
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                instructionEl.style.display = 'none';
            }, 10000);
        }
    }
    
    /**
     * Reset entire process
     */
    resetProcess() {
        if (confirm('Apakah Anda yakin ingin mereset proses penimbangan?')) {
            this.processData = {
                grossWeight: null,
                tareWeight: null,
                netWeight: null,
                truckNumber: '',
                driverName: '',
                poNumber: ''
            };
            
            this.weighingStep = '';
            this.updateProcessFlow('idle');
            this.updateStatusCards();
            this.updateButtonStates(['btn-weigh-in'], true);
            this.updateButtonStates(['btn-weigh-out', 'btn-add-manual'], false);
            
            this.showSuccess('Proses penimbangan telah direset');
        }
    }
    
    /**
     * Update button states
     */
    updateButtonStates(buttonIds, enabled) {
        buttonIds.forEach(id => {
            const button = document.getElementById(id);
            if (button) {
                button.disabled = !enabled;
            }
        });
    }
    
    /**
     * Validate pre-weigh-in conditions
     */
    validatePreWeighIn() {
        // Check if truck number is entered
        const truckNumber = document.getElementById('txt_truck_number')?.value;
        if (!truckNumber) {
            this.showError('Nomor kendaraan harus diisi terlebih dahulu!');
            return false;
        }
        
        // Check if PO is selected
        const poSelection = document.getElementById('cb_po')?.value;
        if (!poSelection) {
            this.showError('PO harus dipilih terlebih dahulu!');
            return false;
        }
        
        return true;
    }
    
    /**
     * Show welcome message with keyboard shortcuts
     */
    showWelcomeMessage() {
        setTimeout(() => {
            this.showInstructions(
                'Sistem Timbangan Siap. Shortcut: Ctrl+1 (Timbang Masuk), Ctrl+2 (Timbang Keluar), Ctrl+M (Manual)'
            );
        }, 1000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.weighbridgeUI = new WeighbridgeUI();
});

/**
 * Integration with existing DevExpress callbacks
 */
function onWeighbridgeCallback(s, e) {
    if (window.weighbridgeUI) {
        // Handle existing callback integration
        window.weighbridgeUI.handleDevExpressCallback(s, e);
    }
}

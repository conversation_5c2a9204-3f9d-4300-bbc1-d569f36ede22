Imports Bright.MVVM
Imports Bright.Controller
Imports DevExpress.Web

Public Class frmSign
    Inherits PageBase

    <Create(Scope:=CreateScope.Session)>
    Property Controller As tm_signController

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_list").ClientVisible = (Controller.SelectedItem Is Nothing)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = (Controller.SelectedItem IsNot Nothing)
    End Sub

    <EventSubscription>
    Public Sub OnMsgChanged(sender As Object, e As EventArgs)
        ltl_msg.Text = Controller.sMsg
    End Sub

    Public Sub New()
        MyBase.New("90201") ' Menu code for Sign Master - adjust as needed
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = False
        End If
    End Sub

    Private Sub ASPxCallbackPanel1_Callback(sender As Object, e As CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If
        Dim s() As String = e.Parameter.ToString.ToLower.Split(";")
        Select Case s(0)
            Case "new"
                Controller.AddNewItem()
            Case "reset"
                Controller.Reset()
            Case "save"
                Me.ucSign_edit.SavingPrepare()
                Controller.Saving()
                If Controller.Saved Then
                    Controller.Reset()
                Else
                    Me.OnEditChanged(Nothing, Nothing)
                End If
            Case "btn_edit"
                Controller.Action = Action.Edit
                Controller.SelectItem(s(1))
            Case "btn_delete"
                Controller.Action = Action.Delete
                Controller.DeleteItem(s(1))
        End Select
    End Sub
End Class
<%@ Page Language="VB" AutoEventWireup="true" MasterPageFile="~/Root.master" CodeBehind="frmWeighbridgeConfig.aspx.vb" Inherits="Bright.frmWeighbridgeConfig" Title="Weighbridge Configuration" %>
<%@ Register Src="~/View/Master/ucWeighbridgeConfig_list.ascx" TagPrefix="uc" TagName="WeighbridgeConfigList" %>
<%@ Register Src="~/View/Master/ucWeighbridgeConfig_edit.ascx" TagPrefix="uc" TagName="WeighbridgeConfigEdit" %>

<asp:Content ID="Content" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" ClientInstanceName="cp_weighbridge" Width="100%">
        <PanelCollection>
            <dx:PanelContent>
                <div class="alert alert-info" runat="server" id="divMsg" visible='<%# Not String.IsNullOrEmpty(ltl_msg.Text) %>'>
                    <asp:Literal ID="ltl_msg" runat="server"></asp:Literal>
                </div>
                
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%">
                    <Items>
                        <dx:LayoutItem Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <uc:WeighbridgeConfigList runat="server" ID="ucWeighbridgeConfig_list" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        
                        <dx:LayoutItem Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer>
                                    <uc:WeighbridgeConfigEdit runat="server" ID="ucWeighbridgeConfig_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>

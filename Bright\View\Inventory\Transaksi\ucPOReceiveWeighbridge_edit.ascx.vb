Imports System
Imports System.Web.UI
Imports Bright.Controller
Imports Bright.Domain
Imports Bright.MVVM
Imports Newtonsoft.Json

Public Class ucPOReceiveWeighbridge_edit
    Inherits UserControl

    <Inject>
    Property Controller As Itr_po_receive_weighbridgeEditController



    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Initialize any data bindings or components on load
    End Sub

    ' Prepare data before saving
    Public Sub SavingPrepare()
        ' Copy form data to ViewModel.SelectedItem (implement if needed)
    End Sub

    ' Process weight save
    Public Sub ProcessWeightSave(weightData As frmPOReceiveWeighbridge.WeightData)
        ' Implement logic to process weight save to ViewModel.SelectedItem
    End Sub

    ' Start weighing process
    Public Sub StartWeighingProcess(stepName As String)
        ' Implement if needed to initiate weigh-in/out processes
    End Sub

    ' Stop weighing process
    Public Sub StopWeighingProcess()
        ' Implement if needed
    End Sub

    ' Event handler untuk perubahan edit state
    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        ' Contoh event handler
    End Sub

End Class


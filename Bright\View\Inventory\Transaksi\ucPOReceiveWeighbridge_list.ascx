﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucPOReceiveWeighbridge_list.ascx.vb" Inherits="Bright.ucPOReceiveWeighbridge_list" %>
<%@ Register assembly="DevExpress.Web.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<style type="text/css">
    .filter-container {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .filter-title {
        font-size: 16px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 15px;
        border-bottom: 2px solid #007bff;
        padding-bottom: 8px;
    }
    
    .filter-row {
        display: flex;
        gap: 20px;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
        min-width: 150px;
    }
    
    .filter-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .grid-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-draft {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .status-weigh-in {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-posted {
        background: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }
    
    .status-cancelled {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>

<div class="filter-container">
    <div class="filter-title">
        <i class="fa fa-filter"></i> Filter Data
    </div>
    
    <div class="filter-row">
        <div class="filter-group">
            <label class="filter-label">Status Posting</label>
            <dx:ASPxCheckBox ID="chk_release" runat="server" CheckState="Unchecked" Text="Show Posted Only">
                <ClientSideEvents ValueChanged="function(s, e) { grd_poreceiveweighbridge.Refresh(); }" />
            </dx:ASPxCheckBox>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Status Transaksi</label>
            <dx:ASPxComboBox ID="cb_status" runat="server" SelectedIndex="0" Width="150px">
                <ClientSideEvents SelectedIndexChanged="function(s, e) { grd_poreceiveweighbridge.Refresh(); }" />
                <Items>
                    <dx:ListEditItem Selected="True" Text="DRAFT" Value="DRAFT" />
                    <dx:ListEditItem Text="WEIGH_IN" Value="WEIGH_IN" />
                    <dx:ListEditItem Text="COMPLETED" Value="COMPLETED" />
                    <dx:ListEditItem Text="POSTED" Value="POSTED" />
                    <dx:ListEditItem Text="CANCELLED" Value="CANCELLED" />
                </Items>
            </dx:ASPxComboBox>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Tanggal Mulai</label>
            <dx:ASPxDateEdit ID="txt_startDate" runat="server" 
                DisplayFormatString="dd MMM yyyy" 
                EditFormat="Custom" 
                EditFormatString="dd-MM-yyyy"
                Width="150px">
                <ClientSideEvents ValueChanged="function(s, e) { grd_poreceiveweighbridge.Refresh(); }" />
            </dx:ASPxDateEdit>
        </div>
        
        <div class="filter-group">
            <label class="filter-label">Tanggal Akhir</label>
            <dx:ASPxDateEdit ID="txt_endDate" runat="server" 
                DisplayFormatString="dd MMM yyyy" 
                EditFormat="Custom" 
                EditFormatString="dd-MM-yyyy"
                Width="150px">
                <ClientSideEvents ValueChanged="function(s, e) { grd_poreceiveweighbridge.Refresh(); }" />
            </dx:ASPxDateEdit>
        </div>
    </div>
</div>

<div class="grid-container">
    <dx:ASPxGridView ID="ASPxGridView1" runat="server" 
        AutoGenerateColumns="False" 
        DataSourceID="EntityServerModeDataSource1" 
        KeyFieldName="Id" 
        Width="100%" 
        ClientInstanceName="grd_poreceiveweighbridge">
        
        <ClientSideEvents 
            CustomButtonClick="function(s, e) {
                var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
                if (e.buttonID != 'btn_print') {
                    if (e.buttonID == 'btn_delete') {
                        if (confirm('Yakin ingin menghapus data ini?')) {
                            cp_poreceiveweighbridge.PerformCallback(e.buttonID + ';' + rowKey);
                        }
                    } else {
                        cp_poreceiveweighbridge.PerformCallback(e.buttonID + ';' + rowKey);
                    }
                } else {
                    wd1.Show();
                }
            }" 
            ToolbarItemClick="function(s, e) {
                switch (e.item.name) { 
                    case 'btn_addnew':
                        cp_poreceiveweighbridge.PerformCallback('new'); 
                        break;
                    case 'btn_refresh':
                        s.Refresh();
                        break;
                }
            }" />
        
        <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="768">
        </SettingsAdaptivity>
        
        <SettingsPager PageSize="25">
            <AllButton Visible="True" />
            <PageSizeItemSettings Visible="True" Items="10,25,50,100" />
        </SettingsPager>
        
        <Settings ShowFilterRowMenu="True" ShowFilterRow="True" ShowFooter="True" />
        <SettingsBehavior AllowFocusedRow="True" />
        <SettingsSearchPanel Visible="True" />
        
        <SettingsExport EnableClientSideExportAPI="True">
        </SettingsExport>
        
        <Columns>
            <dx:GridViewCommandColumn VisibleIndex="0" ShowClearFilterButton="True" Width="120px">
                <CustomButtons>
                    <dx:GridViewCommandColumnCustomButton ID="btn_edit" Text="Edit">
                        <Image IconID="actions_edit_16x16" ToolTip="Edit Data" />
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_view" Text="View">
                        <Image IconID="iconbuilder_security_visibility_svg_16x16" ToolTip="View Data" />
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_delete" Text="Delete">
                        <Image IconID="iconbuilder_actions_delete_svg_16x16" ToolTip="Delete Data" />
                    </dx:GridViewCommandColumnCustomButton>
                    <dx:GridViewCommandColumnCustomButton ID="btn_print1" Text="Print">
                        <Image IconID="reports_report_16x16" ToolTip="Print Report" />
                    </dx:GridViewCommandColumnCustomButton>
                </CustomButtons>
            </dx:GridViewCommandColumn>
            
            <dx:GridViewDataTextColumn FieldName="tm_area.KodeArea" VisibleIndex="1" Caption="Area" Width="80px">
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="tm_cabang.KodeCabang" VisibleIndex="2" Caption="Cabang" Width="80px">
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="tm_lokasi.KodeLokasi" VisibleIndex="3" Caption="Lokasi" Width="80px">
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="NoReceive" VisibleIndex="4" Caption="No. Receive" Width="120px">
                <HeaderStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataDateColumn FieldName="Tanggal" VisibleIndex="5" Caption="Tanggal" Width="100px">
                <PropertiesDateEdit DisplayFormatString="dd MMM yyyy" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataDateColumn>
            
            <dx:GridViewDataTextColumn FieldName="tm_kendaraan.NoPolisi" VisibleIndex="6" Caption="No. Polisi" Width="100px">
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="DriverName" VisibleIndex="7" Caption="Driver" Width="120px">
                <HeaderStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="GrossWeight" VisibleIndex="8" Caption="Gross (kg)" Width="90px">
                <PropertiesTextEdit DisplayFormatString="{0:N2}" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Right" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="TareWeight" VisibleIndex="9" Caption="Tare (kg)" Width="90px">
                <PropertiesTextEdit DisplayFormatString="{0:N2}" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Right" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataTextColumn FieldName="NetWeight" VisibleIndex="10" Caption="Net (kg)" Width="90px">
                <PropertiesTextEdit DisplayFormatString="{0:N2}" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Right" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataDateColumn FieldName="WeighInTime" VisibleIndex="11" Caption="Weigh In" Width="120px">
                <PropertiesDateEdit DisplayFormatString="dd/MM HH:mm" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataDateColumn>
            
            <dx:GridViewDataDateColumn FieldName="WeighOutTime" VisibleIndex="12" Caption="Weigh Out" Width="120px">
                <PropertiesDateEdit DisplayFormatString="dd/MM HH:mm" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataDateColumn>
            
            <dx:GridViewDataTextColumn FieldName="Status" VisibleIndex="13" Caption="Status" Width="100px">
                <DataItemTemplate>
                    <span class='status-badge status-<%# GetStatusClass(Eval("Status")) %>'>
                        <%# Eval("Status") %>
                    </span>
                </DataItemTemplate>
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataCheckColumn FieldName="Posted" VisibleIndex="14" Caption="Posted" Width="70px">
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataCheckColumn>
            
            <dx:GridViewDataTextColumn FieldName="PostedBy" VisibleIndex="15" Caption="Posted By" Width="100px">
                <HeaderStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
            
            <dx:GridViewDataDateColumn FieldName="PostedDate" VisibleIndex="16" Caption="Posted Date" Width="100px">
                <PropertiesDateEdit DisplayFormatString="dd MMM yyyy" />
                <HeaderStyle HorizontalAlign="Center" />
                <CellStyle HorizontalAlign="Center" />
            </dx:GridViewDataDateColumn>
            
            <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="17" Caption="Keterangan" Width="200px">
                <HeaderStyle HorizontalAlign="Center" />
            </dx:GridViewDataTextColumn>
        </Columns>
        
        <TotalSummary>
            <dx:ASPxSummaryItem FieldName="Id" SummaryType="Count" DisplayFormat="Total: {0} records" />
            <dx:ASPxSummaryItem FieldName="GrossWeight" SummaryType="Sum" DisplayFormat="Total Gross: {0:N2} kg" />
            <dx:ASPxSummaryItem FieldName="NetWeight" SummaryType="Sum" DisplayFormat="Total Net: {0:N2} kg" />
        </TotalSummary>
        
        <Toolbars>
            <dx:GridViewToolbar>
                <Items>
                    <dx:GridViewToolbarItem Name="btn_addnew" Text="Add New" ToolTip="Add New Record">
                        <Image IconID="iconbuilder_actions_new_svg_16x16" />
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Name="btn_refresh" Text="Refresh" ToolTip="Refresh Data">
                        <Image IconID="actions_refresh_16x16" />
                    </dx:GridViewToolbarItem>
                    <dx:GridViewToolbarItem Command="ShowFilterRow" BeginGroup="true" />
                    <dx:GridViewToolbarItem Command="ExportToXls" BeginGroup="true" />
                    <dx:GridViewToolbarItem Command="ExportToXlsx" />
                    <dx:GridViewToolbarItem Command="ExportToCsv" />
                    <dx:GridViewToolbarItem Command="ExportToPdf" />
                </Items>
            </dx:GridViewToolbar>
        </Toolbars>
    </dx:ASPxGridView>
</div>

<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" 
    ContextTypeName="Bright.Domain.BrightEntities" 
    TableName="tr_po_receive_weighbridge"
    OnSelecting="EntityServerModeDataSource1_Selecting">
</dx:EntityServerModeDataSource>

﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated. 
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On


Partial Public Class ucPOReceiveWeighbridge_edit
    
    '''<summary>
    '''hf_receive_id control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents hf_receive_id As Global.System.Web.UI.WebControls.HiddenField
    
    '''<summary>
    '''ASPxFormLayout1 control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents ASPxFormLayout1 As Global.DevExpress.Web.ASPxFormLayout
    
    '''<summary>
    '''txt_no_receive control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_no_receive As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''dt_tanggal control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents dt_tanggal As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''txt_status control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_status As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''cb_area control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_area As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_cabang control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_cabang As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_lokasi control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_lokasi As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''cb_po control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_po As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''btn_create_from_po control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_create_from_po As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''txt_keterangan control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_keterangan As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''cb_kendaraan control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_kendaraan As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''btn_add_vehicle control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_add_vehicle As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''txt_driver_name control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_driver_name As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''txt_driver_phone control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_driver_phone As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''btn_weigh_in control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_weigh_in As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_weigh_out control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_weigh_out As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_read_manual control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_read_manual As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_monitor control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_monitor As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_test_weight control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_test_weight As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''txt_gross_weight control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_gross_weight As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_tare_weight control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_tare_weight As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_net_weight control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_net_weight As Global.DevExpress.Web.ASPxSpinEdit
    
    '''<summary>
    '''txt_weight_diff control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_weight_diff As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''dt_weigh_in_time control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents dt_weigh_in_time As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''dt_weigh_out_time control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents dt_weigh_out_time As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''txt_duration control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_duration As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''cb_quality_status control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents cb_quality_status As Global.DevExpress.Web.ASPxComboBox
    
    '''<summary>
    '''chk_posted control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents chk_posted As Global.DevExpress.Web.ASPxCheckBox
    
    '''<summary>
    '''txt_posted_by control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents txt_posted_by As Global.DevExpress.Web.ASPxTextBox
    
    '''<summary>
    '''dt_posted_date control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents dt_posted_date As Global.DevExpress.Web.ASPxDateEdit
    
    '''<summary>
    '''btn_save control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_save As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_post_transaction control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_post_transaction As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_reset control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_reset As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''btn_print control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents btn_print As Global.DevExpress.Web.ASPxButton
    
    '''<summary>
    '''grd_receive_lines control.
    '''</summary>
    '''<remarks>
    '''Auto-generated field.
    '''To modify move field declaration from designer file to code-behind file.
    '''</remarks>
    Protected WithEvents grd_receive_lines As Global.DevExpress.Web.ASPxGridView
End Class

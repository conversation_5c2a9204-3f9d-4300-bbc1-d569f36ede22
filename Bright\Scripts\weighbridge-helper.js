// WeighBridge Helper JavaScript
var WeighBridgeHelper = {
    // Configuration
    apiBaseUrl: '/api/weighbridge',
    currentCabangId: null,
    isMonitoring: false,
    monitoringInterval: null,
    
    // Initialize
    init: function(cabangId) {
        this.currentCabangId = cabangId;
        this.loadStatus();
    },
    
    // Load weighbridge status
    loadStatus: function() {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/status/' + this.currentCabangId,
            type: 'GET',
            success: function(data) {
                WeighBridgeHelper.updateStatusDisplay(data);
            },
            error: function(xhr, status, error) {
                console.error('Error loading weighbridge status:', error);
                WeighBridgeHelper.showMessage('Error loading status: ' + error, 'error');
            }
        });
    },
    
    // Update status display
    updateStatusDisplay: function(status) {
        var statusHtml = '<div class="weighbridge-status">';
        statusHtml += '<h5>' + status.scaleName + ' (' + status.connectionType + ')</h5>';
        statusHtml += '<p>Status: <span class="badge badge-' + (status.isConnected ? 'success' : 'danger') + '">';
        statusHtml += status.isConnected ? 'Connected' : 'Disconnected';
        statusHtml += '</span></p>';
        
        if (status.lastError) {
            statusHtml += '<p class="text-danger">Error: ' + status.lastError + '</p>';
        }
        
        statusHtml += '</div>';
        
        $('#weighbridge-status').html(statusHtml);
        
        // Show dummy controls if it's a dummy connection
        if (status.connectionType === 'DUMMY') {
            this.showDummyControls();
        } else {
            this.hideDummyControls();
        }
    },
    
    // Connect to weighbridge
    connect: function() {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/connect/' + this.currentCabangId,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    WeighBridgeHelper.showMessage('Successfully connected to weighbridge', 'success');
                    WeighBridgeHelper.loadStatus();
                } else {
                    WeighBridgeHelper.showMessage('Connection failed: ' + data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                WeighBridgeHelper.showMessage('Connection error: ' + error, 'error');
            }
        });
    },
    
    // Disconnect from weighbridge
    disconnect: function() {
        if (!this.currentCabangId) return;
        
        this.stopMonitoring();
        
        $.ajax({
            url: this.apiBaseUrl + '/disconnect/' + this.currentCabangId,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    WeighBridgeHelper.showMessage('Successfully disconnected', 'success');
                    WeighBridgeHelper.loadStatus();
                } else {
                    WeighBridgeHelper.showMessage('Disconnect failed: ' + data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                WeighBridgeHelper.showMessage('Disconnect error: ' + error, 'error');
            }
        });
    },
    
    // Get current weight
    getWeight: function(callback) {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/weight/' + this.currentCabangId,
            type: 'GET',
            success: function(data) {
                if (callback) callback(data);
                WeighBridgeHelper.updateWeightDisplay(data);
            },
            error: function(xhr, status, error) {
                console.error('Error getting weight:', error);
                if (callback) callback(null);
            }
        });
    },
    
    // Update weight display
    updateWeightDisplay: function(weightData) {
        if (!weightData) return;
        
        var weightHtml = '<div class="weight-display">';
        weightHtml += '<h4>' + weightData.weight.toFixed(3) + ' ' + weightData.unit + '</h4>';
        weightHtml += '<p>Status: <span class="badge badge-' + (weightData.isStable ? 'success' : 'warning') + '">';
        weightHtml += weightData.isStable ? 'Stable' : 'Unstable';
        weightHtml += '</span></p>';
        weightHtml += '<small>Last update: ' + new Date(weightData.timestamp).toLocaleString() + '</small>';
        weightHtml += '</div>';
        
        $('#weight-display').html(weightHtml);
    },
    
    // Start monitoring weight
    startMonitoring: function(intervalMs) {
        if (this.isMonitoring) return;
        
        intervalMs = intervalMs || 1000; // Default 1 second
        this.isMonitoring = true;
        
        this.monitoringInterval = setInterval(function() {
            WeighBridgeHelper.getWeight();
        }, intervalMs);
        
        this.showMessage('Weight monitoring started', 'info');
        $('#btn-start-monitoring').prop('disabled', true);
        $('#btn-stop-monitoring').prop('disabled', false);
    },
    
    // Stop monitoring weight
    stopMonitoring: function() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        this.showMessage('Weight monitoring stopped', 'info');
        $('#btn-start-monitoring').prop('disabled', false);
        $('#btn-stop-monitoring').prop('disabled', true);
    },
    
    // Dummy-specific functions
    showDummyControls: function() {
        $('#dummy-controls').show();
        this.loadDummyStatus();
    },
    
    hideDummyControls: function() {
        $('#dummy-controls').hide();
    },
    
    loadDummyStatus: function() {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/dummy/status/' + this.currentCabangId,
            type: 'GET',
            success: function(data) {
                if (data.success) {
                    $('#dummy-status').text(data.status);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading dummy status:', error);
            }
        });
    },
    
    simulateWeighIn: function() {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/dummy/weigh-in/' + this.currentCabangId,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    WeighBridgeHelper.showMessage(data.message, 'success');
                    WeighBridgeHelper.loadDummyStatus();
                } else {
                    WeighBridgeHelper.showMessage(data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                WeighBridgeHelper.showMessage('Error: ' + error, 'error');
            }
        });
    },
    
    simulateWeighOut: function() {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/dummy/weigh-out/' + this.currentCabangId,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    WeighBridgeHelper.showMessage(data.message, 'success');
                    WeighBridgeHelper.loadDummyStatus();
                } else {
                    WeighBridgeHelper.showMessage(data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                WeighBridgeHelper.showMessage('Error: ' + error, 'error');
            }
        });
    },
    
    setDummyWeight: function(weight) {
        if (!this.currentCabangId) return;
        
        $.ajax({
            url: this.apiBaseUrl + '/dummy/set-weight/' + this.currentCabangId,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ weight: weight }),
            success: function(data) {
                if (data.success) {
                    WeighBridgeHelper.showMessage(data.message, 'success');
                    WeighBridgeHelper.loadDummyStatus();
                } else {
                    WeighBridgeHelper.showMessage(data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                WeighBridgeHelper.showMessage('Error: ' + error, 'error');
            }
        });
    },
    
    // Test connection
    testConnection: function(configId) {
        $.ajax({
            url: this.apiBaseUrl + '/test/' + configId,
            type: 'POST',
            success: function(data) {
                if (data.success) {
                    WeighBridgeHelper.showMessage('Test connection successful', 'success');
                } else {
                    WeighBridgeHelper.showMessage('Test connection failed: ' + data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                WeighBridgeHelper.showMessage('Test connection error: ' + error, 'error');
            }
        });
    },
    
    // Utility functions
    showMessage: function(message, type) {
        // Create alert element
        var alertClass = 'alert-info';
        switch(type) {
            case 'success': alertClass = 'alert-success'; break;
            case 'error': alertClass = 'alert-danger'; break;
            case 'warning': alertClass = 'alert-warning'; break;
        }
        
        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">';
        alertHtml += message;
        alertHtml += '<button type="button" class="close" data-dismiss="alert" aria-label="Close">';
        alertHtml += '<span aria-hidden="true">&times;</span>';
        alertHtml += '</button>';
        alertHtml += '</div>';
        
        // Show alert
        $('#weighbridge-messages').html(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('#weighbridge-messages .alert').fadeOut();
        }, 5000);
    }
};

// jQuery ready function
$(document).ready(function() {
    // Initialize dummy controls event handlers
    $('#btn-simulate-weigh-in').click(function() {
        WeighBridgeHelper.simulateWeighIn();
    });
    
    $('#btn-simulate-weigh-out').click(function() {
        WeighBridgeHelper.simulateWeighOut();
    });
    
    $('#btn-set-dummy-weight').click(function() {
        var weight = parseFloat($('#txt-dummy-weight').val());
        if (isNaN(weight) || weight < 0) {
            WeighBridgeHelper.showMessage('Please enter a valid weight', 'warning');
            return;
        }
        WeighBridgeHelper.setDummyWeight(weight);
    });
    
    $('#btn-connect').click(function() {
        WeighBridgeHelper.connect();
    });
    
    $('#btn-disconnect').click(function() {
        WeighBridgeHelper.disconnect();
    });
    
    $('#btn-start-monitoring').click(function() {
        WeighBridgeHelper.startMonitoring();
    });
    
    $('#btn-stop-monitoring').click(function() {
        WeighBridgeHelper.stopMonitoring();
    });
    
    $('#btn-get-weight').click(function() {
        WeighBridgeHelper.getWeight();
    });
});

Imports System
Imports System.Web.UI
Imports Bright.Controller
Imports Bright.Domain
Imports Bright.MVVM
Imports Bright.Helpers
Imports Newtonsoft.Json
Imports DevExpress.Web
Imports System.Globalization
Imports System.Linq
Imports Action = Bright.Controller.Action

Public Class ucPOReceiveWeighbridge_edit
    Inherits UserControl

    <Inject>
    Property Controller As Itr_po_receive_weighbridgeEditController

    ' Form controls - declared based on markup to avoid designer conflicts
    Protected WithEvents hf_receive_id As System.Web.UI.WebControls.HiddenField
    Protected WithEvents ASPxFormLayout1 As ASPxFormLayout

    ' Basic Information
    Protected WithEvents txt_no_receive As ASPxTextBox
    Protected WithEvents dt_tanggal As ASPxDateEdit
    Protected WithEvents txt_status As ASPxTextBox
    Protected WithEvents cb_area As ASPxComboBox
    Protected WithEvents cb_cabang As ASPxComboBox
    Protected WithEvents cb_lokasi As ASPxComboBox
    Protected WithEvents cb_po As ASPxComboBox
    Protected WithEvents btn_create_from_po As ASPxButton
    Protected WithEvents txt_keterangan As ASPxMemo

    ' Vehicle Information
    Protected WithEvents cb_kendaraan As ASPxComboBox
    Protected WithEvents btn_add_vehicle As ASPxButton
    Protected WithEvents txt_driver_name As ASPxTextBox
    Protected WithEvents txt_driver_phone As ASPxTextBox

    ' Weighbridge Control Panel
    Protected WithEvents btn_weigh_in As ASPxButton
    Protected WithEvents btn_weigh_out As ASPxButton
    Protected WithEvents btn_monitor As ASPxButton
    Protected WithEvents btn_test_weight As ASPxButton

    ' Weight Information
    Protected WithEvents txt_gross_weight As ASPxSpinEdit
    Protected WithEvents txt_tare_weight As ASPxSpinEdit
    Protected WithEvents txt_net_weight As ASPxSpinEdit
    Protected WithEvents txt_weight_diff As ASPxTextBox
    Protected WithEvents dt_weigh_in_time As ASPxDateEdit
    Protected WithEvents dt_weigh_out_time As ASPxDateEdit
    Protected WithEvents txt_duration As ASPxTextBox
    Protected WithEvents cb_quality_status As ASPxComboBox

    ' Posting Information
    Protected WithEvents chk_posted As ASPxCheckBox
    Protected WithEvents txt_posted_by As ASPxTextBox
    Protected WithEvents dt_posted_date As ASPxDateEdit
    Protected WithEvents txt_created_by As ASPxTextBox
    Protected WithEvents dt_created_date As ASPxDateEdit
    Protected WithEvents txt_modified_by As ASPxTextBox
    Protected WithEvents dt_modified_date As ASPxDateEdit

    ' Action Buttons
    Protected WithEvents btn_save As ASPxButton
    Protected WithEvents btn_post_transaction As ASPxButton
    Protected WithEvents btn_back As ASPxButton
    Protected WithEvents btn_reset As ASPxButton
    Protected WithEvents btn_print As ASPxButton

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        Try
            If Controller.SelectedItem IsNot Nothing Then
                Visible = True

                ' Use standard ASPxFormLayout data binding pattern like SO/PO Receive
                ASPxFormLayout1.DataSource = Controller.SelectedItem
                ASPxFormLayout1.DataBind()

                ' Configure button visibility and text based on action
                ConfigureButtonsBasedOnAction()

                UpdateFormState()
            Else
                Visible = False
            End If
        Catch ex As Exception
            ' Log error and show user-friendly message
            System.Diagnostics.Debug.WriteLine($"Error in OnEditChanged: {ex.Message}")
        End Try
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            InitializeForm()
            RegisterWeighbridgeConfiguration()
        End If
    End Sub

    Private Sub RegisterWeighbridgeConfiguration()
        Try
            ' Register weighbridge configuration for JavaScript
            Dim configJson = WeighbridgeConfigHelper.GetConfigurationJson()
            Dim script = $"var weighbridgeConfig = {configJson};"
            ClientScript.RegisterStartupScript(Me.GetType(), "WeighbridgeConfig", script, True)

            ' Log configuration for debugging
            System.Diagnostics.Debug.WriteLine(WeighbridgeConfigHelper.GetConfigurationSummary())
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error registering weighbridge configuration: {ex.Message}")
        End Try
    End Sub

    Private Sub InitializeForm()
        Try
            ' Set default values
            If dt_tanggal IsNot Nothing Then
                dt_tanggal.Date = DateTime.Today
            End If

            ' Combo boxes will be loaded via DevExpress callback events

            ' Set initial form state
            UpdateFormState()
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error initializing form: {ex.Message}")
        End Try
    End Sub

    Private Sub ConfigureButtonsBasedOnAction()
        Try
            ' Configure button visibility and text based on Controller.Action like other forms
            If btn_save IsNot Nothing Then
                Select Case Controller.Action
                    Case Action.View
                        btn_save.Visible = False
                    Case Action.Posting
                        btn_save.Visible = True
                        btn_save.Text = "Post Transaction"
                    Case Action.UnPosting
                        btn_save.Visible = True
                        btn_save.Text = "UnPost Transaction"
                    Case Else ' AddNew, Edit
                        btn_save.Visible = True
                        btn_save.Text = "Save"
                End Select
            End If

            ' Configure other action buttons
            If btn_post_transaction IsNot Nothing Then
                btn_post_transaction.Visible = (Controller.Action <> Action.View)
            End If

            ' Back button is always visible for navigation
            If btn_back IsNot Nothing Then
                btn_back.Visible = True
            End If

            ' Reset and Print buttons visibility
            If btn_reset IsNot Nothing Then
                btn_reset.Visible = True
            End If

            If btn_print IsNot Nothing Then
                btn_print.Visible = True
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error configuring buttons: {ex.Message}")
        End Try
    End Sub

#Region "Cascading Dropdown Methods"

    ' Area dropdown methods
    Protected Sub cb_area_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_area.ItemRequestedByValue
        myMethod.Area_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_area_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_area.ItemsRequestedByFilterCondition
        myMethod.Area_ItemsRequestedByFilterCondition(source, e)
    End Sub

    ' Cabang dropdown methods - depends on Area
    Private Sub cb_cabang_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_cabang.ItemRequestedByValue
        myMethod.Cabang_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_cabang_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_cabang.ItemsRequestedByFilterCondition
        myMethod.Cabang_ItemsRequestedByFilterCondition(source, e, cb_area.Value, False)
    End Sub

    ' Lokasi dropdown methods - depends on Cabang
    Private Sub cb_lokasi_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_lokasi.ItemRequestedByValue
        myMethod.Lokasi_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_lokasi_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_lokasi.ItemsRequestedByFilterCondition
        myMethod.Lokasi_ItemsRequestedByFilterCondition(source, e, cb_cabang.Value)
    End Sub

    ' PO dropdown methods
    Private Sub cb_po_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_po.ItemRequestedByValue
        myMethod.PO_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_po_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_po.ItemsRequestedByFilterCondition
        myMethod.PO_ItemsRequestedByFilterCondition(source, e, False)
    End Sub

    ' Kendaraan dropdown methods
    Private Sub cb_kendaraan_ItemRequestedByValue(source As Object, e As ListEditItemRequestedByValueEventArgs) Handles cb_kendaraan.ItemRequestedByValue
        myMethod.Kendaraan_ItemRequestedByValue(source, e)
    End Sub

    Private Sub cb_kendaraan_ItemsRequestedByFilterCondition(source As Object, e As ListEditItemsRequestedByFilterConditionEventArgs) Handles cb_kendaraan.ItemsRequestedByFilterCondition
        myMethod.Kendaraan_ItemsRequestedByFilterCondition(source, e)
    End Sub

#End Region



    ' LoadDataToForm is no longer needed - ASPxFormLayout handles data binding automatically

    Private Sub SetControlValue(control As Object, value As Object)
        Try
            If control Is Nothing Then Return

            Select Case True
                Case TypeOf control Is ASPxTextBox
                    DirectCast(control, ASPxTextBox).Text = If(value?.ToString(), "")

                Case TypeOf control Is ASPxMemo
                    DirectCast(control, ASPxMemo).Text = If(value?.ToString(), "")

                Case TypeOf control Is ASPxDateEdit
                    Dim dateControl = DirectCast(control, ASPxDateEdit)
                    If TypeOf value Is DateTime? Then
                        dateControl.Date = DirectCast(value, DateTime?)
                    ElseIf TypeOf value Is DateTime Then
                        dateControl.Date = DirectCast(value, DateTime)
                    Else
                        dateControl.Date = Nothing
                    End If

                Case TypeOf control Is ASPxComboBox
                    Dim comboControl = DirectCast(control, ASPxComboBox)
                    If value IsNot Nothing Then
                        comboControl.Value = value
                    Else
                        comboControl.Value = Nothing
                    End If

                Case TypeOf control Is ASPxSpinEdit
                    Dim spinControl = DirectCast(control, ASPxSpinEdit)
                    If TypeOf value Is Decimal? Then
                        spinControl.Value = DirectCast(value, Decimal?)
                    ElseIf TypeOf value Is Decimal Then
                        spinControl.Value = DirectCast(value, Decimal)
                    Else
                        spinControl.Value = Nothing
                    End If

                Case TypeOf control Is ASPxCheckBox
                    Dim checkControl = DirectCast(control, ASPxCheckBox)
                    If TypeOf value Is Boolean? Then
                        checkControl.Checked = DirectCast(value, Boolean?).GetValueOrDefault(False)
                    ElseIf TypeOf value Is Boolean Then
                        checkControl.Checked = DirectCast(value, Boolean)
                    Else
                        checkControl.Checked = False
                    End If
            End Select

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error setting control value: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateDurationDisplay()
        Try
            If txt_duration IsNot Nothing AndAlso Controller.SelectedItem IsNot Nothing Then
                Dim item = Controller.SelectedItem
                If item.WeighInTime.HasValue AndAlso item.WeighOutTime.HasValue Then
                    Dim duration = item.WeighOutTime.Value - item.WeighInTime.Value
                    txt_duration.Text = $"{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}"
                Else
                    txt_duration.Text = ""
                End If
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error updating duration display: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateFormState()
        Try
            Dim item = Controller.SelectedItem
            If item Is Nothing Then Return

            Dim isPosted = item.Posted.GetValueOrDefault(False)
            Dim status = If(item.Status, "DRAFT")

            ' Disable editing if posted
            Dim isEditable = Not isPosted

            ' Enable/disable controls based on status
            EnableFormControls(isEditable)

            ' Update weighbridge control panel state
            UpdateWeighbridgeControlState(status)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error updating form state: {ex.Message}")
        End Try
    End Sub

    Private Sub EnableFormControls(enabled As Boolean)
        Try
            ' Basic controls
            If dt_tanggal IsNot Nothing Then dt_tanggal.ReadOnly = Not enabled
            If cb_area IsNot Nothing Then cb_area.ReadOnly = Not enabled
            If cb_cabang IsNot Nothing Then cb_cabang.ReadOnly = Not enabled
            If cb_lokasi IsNot Nothing Then cb_lokasi.ReadOnly = Not enabled
            If cb_po IsNot Nothing Then cb_po.ReadOnly = Not enabled
            If cb_kendaraan IsNot Nothing Then cb_kendaraan.ReadOnly = Not enabled
            If txt_driver_name IsNot Nothing Then txt_driver_name.ReadOnly = Not enabled
            If txt_driver_phone IsNot Nothing Then txt_driver_phone.ReadOnly = Not enabled
            If txt_keterangan IsNot Nothing Then txt_keterangan.ReadOnly = Not enabled

            ' Weight controls are always readonly (controlled by weighbridge)
            If txt_gross_weight IsNot Nothing Then txt_gross_weight.ReadOnly = True
            If txt_tare_weight IsNot Nothing Then txt_tare_weight.ReadOnly = True
            If txt_net_weight IsNot Nothing Then txt_net_weight.ReadOnly = True
            If dt_weigh_in_time IsNot Nothing Then dt_weigh_in_time.ReadOnly = True
            If dt_weigh_out_time IsNot Nothing Then dt_weigh_out_time.ReadOnly = True

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error enabling/disabling form controls: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateWeighbridgeControlState(status As String)
        Try
            ' This will be called from JavaScript to update weighbridge control panel
            Dim script As String = $"updateWeighbridgeControlState('{status}');"
            ScriptManager.RegisterStartupScript(Me, Me.GetType(), "updateWeighbridgeState", script, True)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error updating weighbridge control state: {ex.Message}")
        End Try
    End Sub

    Private Sub ClearForm()
        Try
            ' Clear all form controls
            SetControlValue(txt_no_receive, "")
            SetControlValue(dt_tanggal, DateTime.Today)
            SetControlValue(cb_area, Nothing)
            SetControlValue(cb_cabang, Nothing)
            SetControlValue(cb_lokasi, Nothing)
            SetControlValue(cb_po, Nothing)
            SetControlValue(cb_kendaraan, Nothing)
            SetControlValue(txt_driver_name, "")
            SetControlValue(txt_driver_phone, "")
            SetControlValue(txt_keterangan, "")
            SetControlValue(txt_gross_weight, Nothing)
            SetControlValue(txt_tare_weight, Nothing)
            SetControlValue(txt_net_weight, Nothing)
            SetControlValue(dt_weigh_in_time, Nothing)
            SetControlValue(dt_weigh_out_time, Nothing)
            SetControlValue(txt_duration, "")
            SetControlValue(txt_status, "DRAFT")
            SetControlValue(chk_posted, False)
            SetControlValue(txt_posted_by, "")
            SetControlValue(dt_posted_date, Nothing)
            SetControlValue(txt_created_by, "")
            SetControlValue(dt_created_date, Nothing)
            SetControlValue(txt_modified_by, "")
            SetControlValue(dt_modified_date, Nothing)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error clearing form: {ex.Message}")
        End Try
    End Sub

    ' Prepare data before saving - using standard ASPxFormLayout pattern
    Public Sub SavingPrepare()
        Try
            ' Use standard ASPxFormLayout pattern like SO/PO Receive
            With Controller.SelectedItem
                ' Basic Information
                .NoReceive = ASPxFormLayout1.GetNestedControlValueByFieldName("NoReceive")
                .Tanggal = ASPxFormLayout1.GetNestedControlValueByFieldName("Tanggal")
                .Area_id = ASPxFormLayout1.GetNestedControlValueByFieldName("Area_id")
                .Cabang_id = ASPxFormLayout1.GetNestedControlValueByFieldName("Cabang_id")
                .Lokasi_id = ASPxFormLayout1.GetNestedControlValueByFieldName("Lokasi_id")
                ' PO relationship is handled through weighbridge lines, not direct PO_id
                .Kendaraan_id = ASPxFormLayout1.GetNestedControlValueByFieldName("Kendaraan_id")
                .DriverName = ASPxFormLayout1.GetNestedControlValueByFieldName("DriverName")
                .DriverPhone = ASPxFormLayout1.GetNestedControlValueByFieldName("DriverPhone")
                .Keterangan = ASPxFormLayout1.GetNestedControlValueByFieldName("Keterangan")

                ' Weight Information
                .GrossWeight = ASPxFormLayout1.GetNestedControlValueByFieldName("GrossWeight")
                .TareWeight = ASPxFormLayout1.GetNestedControlValueByFieldName("TareWeight")
                .NetWeight = ASPxFormLayout1.GetNestedControlValueByFieldName("NetWeight")
                .WeighInTime = ASPxFormLayout1.GetNestedControlValueByFieldName("WeighInTime")
                .WeighOutTime = ASPxFormLayout1.GetNestedControlValueByFieldName("WeighOutTime")
                ' QualityStatus is at line level (tr_po_receive_weighbridge_line), not header level

                ' Status
                .Status = ASPxFormLayout1.GetNestedControlValueByFieldName("Status")
                .Posted = ASPxFormLayout1.GetNestedControlValueByFieldName("Posted")

                ' Calculate net weight if both gross and tare are available
                If .GrossWeight.HasValue AndAlso .TareWeight.HasValue Then
                    .NetWeight = .GrossWeight.Value - .TareWeight.Value
                End If

                ' Set audit fields
                If .Id = 0 Then ' New record
                    .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                    .CreatedDate = DateTime.Now
                Else ' Existing record
                    .ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                    .ModifiedDate = DateTime.Now
                End If
            End With

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in SavingPrepare: {ex.Message}")
            Throw New Exception($"Error preparing data for save: {ex.Message}", ex)
        End Try
    End Sub

    Private Function GetControlValue(Of T)(control As Object) As T
        Try
            If control Is Nothing Then Return Nothing

            Select Case True
                Case TypeOf control Is ASPxTextBox
                    Dim textValue = DirectCast(control, ASPxTextBox).Text
                    Return ConvertValue(Of T)(textValue)

                Case TypeOf control Is ASPxMemo
                    Dim memoValue = DirectCast(control, ASPxMemo).Text
                    Return ConvertValue(Of T)(memoValue)

                Case TypeOf control Is ASPxDateEdit
                    Dim dateValue = DirectCast(control, ASPxDateEdit).Date
                    Return ConvertValue(Of T)(dateValue)

                Case TypeOf control Is ASPxComboBox
                    Dim comboValue = DirectCast(control, ASPxComboBox).Value
                    Return ConvertValue(Of T)(comboValue)

                Case TypeOf control Is ASPxSpinEdit
                    Dim spinValue = DirectCast(control, ASPxSpinEdit).Value
                    Return ConvertValue(Of T)(spinValue)

                Case TypeOf control Is ASPxCheckBox
                    Dim checkValue = DirectCast(control, ASPxCheckBox).Checked
                    Return ConvertValue(Of T)(checkValue)

                Case Else
                    Return Nothing
            End Select

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting control value: {ex.Message}")
            Return Nothing
        End Try
    End Function

    Private Function ConvertValue(Of T)(value As Object) As T
        Try
            If value Is Nothing Then Return Nothing

            Dim targetType = GetType(T)

            ' Handle nullable types
            If targetType.IsGenericType AndAlso targetType.GetGenericTypeDefinition() = GetType(Nullable(Of )) Then
                If value Is Nothing OrElse value Is DBNull.Value Then
                    Return Nothing
                End If
                targetType = Nullable.GetUnderlyingType(targetType)
            End If

            ' Handle string conversion
            If targetType = GetType(String) Then
                Return DirectCast(CObj(value?.ToString()), T)
            End If

            ' Handle other type conversions
            Return DirectCast(Convert.ChangeType(value, targetType, CultureInfo.InvariantCulture), T)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error converting value: {ex.Message}")
            Return Nothing
        End Try
    End Function

    ' Process weight save from weighbridge
    Public Sub ProcessWeightSave(weightData As frmPOReceiveWeighbridge.WeightData)
        Try
            If Controller.SelectedItem Is Nothing Then
                Throw New InvalidOperationException("No item selected for weight save")
            End If

            If weightData Is Nothing Then
                Throw New ArgumentNullException(NameOf(weightData), "Weight data cannot be null")
            End If

            ' Convert to domain weight data
            Dim domainWeightData = weightData.ToDomainWeightData()

            With Controller.SelectedItem
                Select Case weightData.step?.ToUpper()
                    Case "WEIGH_IN"
                        .GrossWeight = domainWeightData.Weight
                        .WeighInTime = domainWeightData.Timestamp
                        .Status = "WEIGH_IN"

                        ' Update form display
                        SetControlValue(txt_gross_weight, .GrossWeight)
                        SetControlValue(dt_weigh_in_time, .WeighInTime)
                        SetControlValue(txt_status, .Status)

                    Case "WEIGH_OUT"
                        .TareWeight = domainWeightData.Weight
                        .WeighOutTime = domainWeightData.Timestamp
                        .Status = "COMPLETED"

                        ' Calculate net weight
                        If .GrossWeight.HasValue Then
                            .NetWeight = .GrossWeight.Value - domainWeightData.Weight
                        End If

                        ' Update form display
                        SetControlValue(txt_tare_weight, .TareWeight)
                        SetControlValue(dt_weigh_out_time, .WeighOutTime)
                        SetControlValue(txt_net_weight, .NetWeight)
                        SetControlValue(txt_status, .Status)

                        ' Update duration
                        UpdateDurationDisplay()

                    Case Else
                        Throw New ArgumentException($"Invalid weighing step: {weightData.step}")
                End Select

                ' Set audit fields
                .ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                .ModifiedDate = DateTime.Now
            End With

            ' Update form state
            UpdateFormState()

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in ProcessWeightSave: {ex.Message}")
            Throw New Exception($"Error processing weight save: {ex.Message}", ex)
        End Try
    End Sub

    ' Start weighing process
    Public Sub StartWeighingProcess(stepName As String)
        Try
            If Controller.SelectedItem Is Nothing Then
                Throw New InvalidOperationException("No item selected for weighing process")
            End If

            ' Validate prerequisites based on step
            Select Case stepName?.ToUpper()
                Case "WEIGH_IN"
                    ' Validate basic information is complete
                    ValidateBasicInformation()

                Case "WEIGH_OUT"
                    ' Validate that weigh-in is completed
                    If Not Controller.SelectedItem.GrossWeight.HasValue OrElse
                       Not Controller.SelectedItem.WeighInTime.HasValue Then
                        Throw New InvalidOperationException("Weigh-in must be completed before weigh-out")
                    End If

                Case Else
                    Throw New ArgumentException($"Invalid weighing step: {stepName}")
            End Select

            ' Update status and form
            Controller.SelectedItem.Status = stepName.ToUpper()
            SetControlValue(txt_status, Controller.SelectedItem.Status)
            UpdateFormState()

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in StartWeighingProcess: {ex.Message}")
            Throw New Exception($"Error starting weighing process: {ex.Message}", ex)
        End Try
    End Sub

    ' Stop weighing process
    Public Sub StopWeighingProcess()
        Try
            ' Reset weighing state
            Dim script As String = "stopMonitoring();"
            ScriptManager.RegisterStartupScript(Me, Me.GetType(), "stopWeighing", script, True)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in StopWeighingProcess: {ex.Message}")
        End Try
    End Sub

    Private Sub ValidateBasicInformation()
        Dim errors As New List(Of String)

        If GetControlValue(Of Integer)(cb_cabang) <= 0 Then
            errors.Add("Branch must be selected")
        End If

        If GetControlValue(Of Integer)(cb_lokasi) <= 0 Then
            errors.Add("Location must be selected")
        End If

        If GetControlValue(Of Integer)(cb_kendaraan) <= 0 Then
            errors.Add("Vehicle must be selected")
        End If

        If String.IsNullOrWhiteSpace(GetControlValue(Of String)(txt_driver_name)) Then
            errors.Add("Driver name must be filled")
        End If

        If errors.Any() Then
            Throw New ValidationException($"Validation failed: {String.Join("; ", errors)}")
        End If
    End Sub

    ' Public method to refresh form data
    Public Sub RefreshForm()
        Try
            ' Use ASPxFormLayout data binding instead of manual LoadDataToForm
            If Controller.SelectedItem IsNot Nothing Then
                ASPxFormLayout1.DataSource = Controller.SelectedItem
                ASPxFormLayout1.DataBind()
            End If
            UpdateFormState()
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error refreshing form: {ex.Message}")
        End Try
    End Sub

    ' Public method to get current cabang ID for JavaScript
    Public Function GetCurrentCabangId() As Integer
        Try
            Return GetControlValue(Of Integer)(cb_cabang)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting current cabang ID: {ex.Message}")
            Return 0
        End Try
    End Function

End Class

' Custom exception for validation errors
Public Class ValidationException
    Inherits Exception

    Public Sub New(message As String)
        MyBase.New(message)
    End Sub

    Public Sub New(message As String, innerException As Exception)
        MyBase.New(message, innerException)
    End Sub
End Class


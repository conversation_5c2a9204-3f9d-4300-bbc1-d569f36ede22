﻿Imports DevExpress.Web
Imports Bright.Controller
Imports Bright.MVVM
Imports Bright.Helpers
Imports Newtonsoft.Json

Public Class frmPOReceiveWeighbridge
    Inherits PageBase

    <Create(Scope:=CreateScope.Session, Id:="frmPOReceiveWeighbridge")>
    Property Controller As tr_po_receive_weighbridgeController

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_list").ClientVisible = (Controller.SelectedItem Is Nothing)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = (Controller.SelectedItem IsNot Nothing)
    End Sub

    <EventSubscription>
    Public Sub OnMsgChanged(sender As Object, e As EventArgs)
        ltl_msg.Text = If(String.IsNullOrEmpty(Controller.sMsg), "",
            $"<div class='alert alert-{If(Controller.Saved, "success", "danger")}' role='alert'>" &
            $"<i class='fa fa-{If(Controller.Saved, "check", "exclamation-triangle")}'></i> {Controller.sMsg}</div>")
    End Sub

    <EventSubscription>
    Public Sub OnPrinting(sender As Object, e As EventArgs)
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_list").ClientVisible = False
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = False
        Me.ASPxFormLayout1.FindItemOrGroupByName("li_print").ClientVisible = True

        Me.ucPrintPreview.setButtonBack("cp_poreceiveweighbridge")
        Me.ucPrintPreview.ShowRpt(Controller.SelectedPrint)
    End Sub

    Public Sub New()
        MyBase.New("20223") ' Menu ID untuk PO Receive Weighbridge
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            ' Hide edit and print panels initially
            Me.ASPxFormLayout1.FindItemOrGroupByName("li_edit").ClientVisible = False
            Me.ASPxFormLayout1.FindItemOrGroupByName("li_print").ClientVisible = False

            ' Initialize weighbridge monitoring for current branch
            Dim cabangId As Integer = AuthHelper.GetLoggedInUserInfo.Cabang_id
            ClientScript.RegisterStartupScript(Me.GetType(), "initWeighbridge",
                $"initializeWeighbridge({cabangId});", True)
        End If
    End Sub

    Private Sub ASPxCallbackPanel1_Callback(sender As Object, e As CallbackEventArgsBase) Handles ASPxCallbackPanel1.Callback
        If String.IsNullOrEmpty(e.Parameter) Then
            Return
        End If

        Dim s() As String = e.Parameter.ToString.ToLower.Split(";")

        Try
            Select Case s(0)
                Case "new"
                    Controller.AddNewItem()

                Case "reset"
                    Controller.Reset()

                Case "save"
                    Me.ucPOReceiveWeighbridge_edit.SavingPrepare()
                    Controller.Saving()
                    If Controller.Saved Then
                        Controller.Reset()
                    Else
                        Me.OnEditChanged(Nothing, Nothing)
                        Me.ucPOReceiveWeighbridge_edit.OnEditChanged(Nothing, Nothing)
                    End If

                Case "save_weight"
                    ' Handle weighbridge weight saving
                    If s.Length > 1 Then
                        Try
                            ' Parse weight data from JSON
                            Dim weightData = JsonConvert.DeserializeObject(Of WeightData)(s(1))

                            ' Call SavingPrepare first to copy form data to Controller.SelectedItem
                            Me.ucPOReceiveWeighbridge_edit.SavingPrepare()

                            ' Process weight save through edit control
                            Me.ucPOReceiveWeighbridge_edit.ProcessWeightSave(weightData)

                            ' Update UI
                            Me.OnEditChanged(Nothing, Nothing)
                            Me.ucPOReceiveWeighbridge_edit.OnEditChanged(Nothing, Nothing)

                        Catch ex As ValidationException
                            Controller.sMsg = "Validation Error: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As InvalidOperationException
                            Controller.sMsg = "Operation Error: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As ArgumentException
                            Controller.sMsg = "Invalid Data: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As Exception
                            ' Log the full exception for debugging
                            System.Diagnostics.Debug.WriteLine($"Unexpected error in weight save: {ex}")
                            Controller.sMsg = "An unexpected error occurred while saving weight data. Please contact support."
                            Me.OnMsgChanged(Nothing, Nothing)
                        End Try
                    End If

                Case "start_weighing"
                    ' Initialize weighing process
                    If s.Length > 1 Then
                        Try
                            Me.ucPOReceiveWeighbridge_edit.SavingPrepare()
                            Me.ucPOReceiveWeighbridge_edit.StartWeighingProcess(s(1))
                            Me.OnEditChanged(Nothing, Nothing)
                            Me.ucPOReceiveWeighbridge_edit.OnEditChanged(Nothing, Nothing)
                        Catch ex As ValidationException
                            Controller.sMsg = "Validation Error: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As Exception
                            Controller.sMsg = "Error starting weighing process: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        End Try
                    End If

                Case "stop_weighing"
                    ' Stop weighing process
                    Me.ucPOReceiveWeighbridge_edit.StopWeighingProcess()

                Case "create_from_po"
                    ' Create receive from PO
                    If s.Length > 3 Then
                        Try
                            Dim poId As Integer = CInt(s(1))
                            Dim cabangId As Integer = CInt(s(2))
                            Dim lokasiId As Integer = CInt(s(3))

                            Dim result = Controller.CreateFromPO(poId, cabangId, lokasiId)
                            If result.Success Then
                                Controller.SelectItem(result.ReceiveId.ToString())
                                Controller.sMsg = result.Message
                            Else
                                Controller.sMsg = result.Message
                            End If

                            Me.OnEditChanged(Nothing, Nothing)
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As ArgumentException
                            Controller.sMsg = "Invalid parameters: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As Exception
                            Controller.sMsg = "Error creating receive from PO: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        End Try
                    End If

                Case "post_transaction"
                    ' Post transaction to inventory
                    If s.Length > 1 Then
                        Try
                            Dim receiveId As Integer = CInt(s(1))
                            Dim result = Controller.PostTransaction(receiveId)

                            Controller.sMsg = result.Message
                            Controller.Saved = result.Success

                            If result.Success Then
                                ' Refresh the item to show updated status
                                Controller.SelectItem(receiveId.ToString())
                                Me.ucPOReceiveWeighbridge_edit.OnEditChanged(Nothing, Nothing)
                            End If

                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As ArgumentException
                            Controller.sMsg = "Invalid receive ID: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        Catch ex As Exception
                            Controller.sMsg = "Error posting transaction: " & ex.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        End Try
                    End If

                Case "btn_edit"
                    Controller.Action = Action.Edit
                    Controller.SelectItem(s(1))

                Case "btn_view"
                    Controller.Action = Action.View
                    Controller.SelectItem(s(1))

                Case "btn_delete"
                    Controller.Action = Action.Delete
                    Controller.DeleteItem(s(1))

                Case "btn_print1"
                    Controller.SelectPrint(s(1))

                Case "get_current_weight"
                    ' Get real-time weight for specific cabang
                    If s.Length > 1 Then
                        Dim cabangId As Integer = CInt(s(1))
                        Dim result = Controller.GetCurrentWeight(cabangId)

                        If result.Success Then
                            ' Return weight data as JSON to client
                            Dim weightJson = JsonConvert.SerializeObject(result.WeightData)
                            ClientScript.RegisterStartupScript(Me.GetType(), "updateWeight",
                                $"updateWeightDisplay({weightJson});", True)
                        Else
                            Controller.sMsg = result.Message
                            Me.OnMsgChanged(Nothing, Nothing)
                        End If
                    End If

            End Select

        Catch ex As Exception
            Controller.sMsg = $"Error processing request: {ex.Message}"
            Controller.Saved = False
            Me.OnMsgChanged(Nothing, Nothing)
        End Try
    End Sub

    ' Helper class for JSON deserialization - using standardized WeightData
    Public Class WeightData
        Public Property weight As Decimal
        Public Property unit As String = "kg"
        Public Property timestamp As String
        Public Property scaleId As String
        Public Property isStable As Boolean = True
        Public Property [step] As String = "idle"
        Public Property rawData As String

        ' Convert to domain WeightData
        Public Function ToDomainWeightData() As Bright.Helpers.WeighBridge.WeightData
            Return New Bright.Helpers.WeighBridge.WeightData With {
                .Weight = Me.weight,
                .Unit = Me.unit,
                .Timestamp = If(DateTime.TryParse(Me.timestamp, Nothing), DateTime.Parse(Me.timestamp), DateTime.Now),
                .IsStable = Me.isStable,
                .ScaleId = Me.scaleId,
                .RawData = Me.rawData
            }
        End Function
    End Class
End Class

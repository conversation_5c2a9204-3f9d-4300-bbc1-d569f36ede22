﻿Imports System
Imports System.Linq
Imports Bright.MVVM
Imports Bright.Domain

Public Class ucPOReceiveWeighbridge_list
    Inherits UserControl

    Private _vm As tr_po_receive_weighbridgeViewModel
    Protected WithEvents chk_release As Global.DevExpress.Web.ASPxCheckBox
    Protected WithEvents cb_status As Global.DevExpress.Web.ASPxComboBox
    Protected WithEvents txt_startDate As Global.DevExpress.Web.ASPxDateEdit
    Protected WithEvents txt_endDate As Global.DevExpress.Web.ASPxDateEdit

    <Create(Scope:=CreateScope.Session, Id:="ucPOReceiveWeighbridge_list")>
    Public Property ViewModel As tr_po_receive_weighbridgeViewModel
        Get
            Return _vm
        End Get
        Set(value As tr_po_receive_weighbridgeViewModel)
            _vm = value
        End Set
    End Property

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
    End Sub

    Protected Sub EntityServerModeDataSource1_Selecting(sender As Object, e As DevExpress.Data.Linq.LinqServerModeDataSourceSelectEventArgs)
        Dim db As New BrightEntities

        e.KeyExpression = "Id"

        ' Preparing query with filters
        Dim query = db.tr_po_receive_weighbridge.AsNoTracking().Select(Function(receive) New With {
            .Id = receive.Id,
            .NoReceive = receive.NoReceive,
            .Tanggal = receive.Tanggal,
            .Keterangan = receive.Keterangan,
            .Status = receive.Status,
            .Posted = receive.Posted,
            .PostedBy = receive.PostedBy,
            .PostedDate = receive.PostedDate,
            .GrossWeight = receive.GrossWeight,
            .NetWeight = receive.NetWeight,
            .TareWeight = receive.TareWeight,
            .WeighInTime = receive.WeighInTime,
            .WeighOutTime = receive.WeighOutTime,
            .tm_area = New With {.KodeArea = receive.tm_area.KodeArea},
            .tm_cabang = New With {.KodeCabang = receive.tm_cabang.KodeCabang},
            .tm_lokasi = New With {.KodeLokasi = receive.tm_lokasi.KodeLokasi},
            .tm_kendaraan = New With {.NoPolisi = receive.tm_kendaraan.NoPolisi},
            .DriverName = receive.DriverName
        })

        If chk_release.Checked Then
            query = query.Where(Function(receive) receive.Posted = True)
        End If

        Dim selectedStatus As String = cb_status.Value
        If Not String.IsNullOrEmpty(selectedStatus) Then
            query = query.Where(Function(receive) receive.Status = selectedStatus)
        End If

        If txt_startDate.Value IsNot Nothing Then
            Dim startDate As Date = txt_startDate.Value
            query = query.Where(Function(receive) receive.Tanggal >= startDate)
        End If

        If txt_endDate.Value IsNot Nothing Then
            Dim endDate As Date = txt_endDate.Value.AddDays(1).AddSeconds(-1) ' End of the day
            query = query.Where(Function(receive) receive.Tanggal <= endDate)
        End If

        e.QueryableSource = query
    End Sub

    ' Helper function untuk status class
    Protected Function GetStatusClass(status As Object) As String
        If status Is Nothing Then Return "draft"

        Dim statusStr As String = status.ToString().ToLower()
        Select Case statusStr
            Case "draft"
                Return "draft"
            Case "weigh_in", "weigh-in"
                Return "weigh-in"
            Case "completed"
                Return "completed"
            Case "posted"
                Return "posted"
            Case "cancelled"
                Return "cancelled"
            Case Else
                Return "draft"
        End Select
    End Function

End Class


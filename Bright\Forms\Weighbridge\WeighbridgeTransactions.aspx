<%@ Page Title="Weighbridge Transactions" Language="VB" MasterPageFile="~/Root.master" AutoEventWireup="false" CodeBehind="WeighbridgeTransactions.aspx.vb" Inherits="Bright.WeighbridgeTransactions" %>

<asp:Content ID="Content1" ContentPlaceHolderID="Head" runat="server">
    <style>
        .transaction-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .transaction-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .filter-panel {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        
        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .transactions-grid {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .grid-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }
        
        .transaction-card {
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
            transition: background-color 0.3s ease;
        }
        
        .transaction-card:hover {
            background-color: #f8f9fa;
        }
        
        .transaction-card:last-child {
            border-bottom: none;
        }
        
        .transaction-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            align-items: center;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 2px;
        }
        
        .info-value {
            font-weight: bold;
            color: #333;
        }
        
        .weight-display {
            font-size: 1.2em;
            color: #667eea;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .pagination {
            text-align: center;
            margin-top: 20px;
        }
        
        .pagination a {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 2px;
            text-decoration: none;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #667eea;
        }
        
        .pagination a:hover {
            background-color: #667eea;
            color: white;
        }
        
        .pagination .current {
            background-color: #667eea;
            color: white;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .summary-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #dee2e6;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="PageContent" runat="server">
    <div class="transaction-container">
        <!-- Header -->
        <div class="transaction-header">
            <h1><i class="fas fa-list-alt"></i> Weighbridge Transactions</h1>
            <p>View and manage all weighbridge transactions</p>
        </div>
        
        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-value" id="totalTransactions">
                    <asp:Label ID="TotalTransactionsLabel" runat="server" Text="0"></asp:Label>
                </div>
                <div class="summary-label">Total Transactions</div>
            </div>
            <div class="summary-card">
                <div class="summary-value" id="todayTransactions">
                    <asp:Label ID="TodayTransactionsLabel" runat="server" Text="0"></asp:Label>
                </div>
                <div class="summary-label">Today's Transactions</div>
            </div>
            <div class="summary-card">
                <div class="summary-value" id="totalWeight">
                    <asp:Label ID="TotalWeightLabel" runat="server" Text="0.00"></asp:Label>
                </div>
                <div class="summary-label">Total Weight (kg)</div>
            </div>
            <div class="summary-card">
                <div class="summary-value" id="avgWeight">
                    <asp:Label ID="AverageWeightLabel" runat="server" Text="0.00"></asp:Label>
                </div>
                <div class="summary-label">Average Weight (kg)</div>
            </div>
        </div>
        
        <!-- Filter Panel -->
        <div class="filter-panel">
            <h3><i class="fas fa-filter"></i> Filter Transactions</h3>
            <div class="filter-row">
                <div class="form-group">
                    <label>From Date:</label>
                    <asp:TextBox ID="FromDateTextBox" runat="server" TextMode="Date" CssClass="form-control"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>To Date:</label>
                    <asp:TextBox ID="ToDateTextBox" runat="server" TextMode="Date" CssClass="form-control"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>Vehicle Number:</label>
                    <asp:TextBox ID="VehicleNumberTextBox" runat="server" CssClass="form-control" placeholder="Enter vehicle number"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>Driver Name:</label>
                    <asp:TextBox ID="DriverNameTextBox" runat="server" CssClass="form-control" placeholder="Enter driver name"></asp:TextBox>
                </div>
            </div>
            <div class="filter-row">
                <div class="form-group">
                    <label>Status:</label>
                    <asp:DropDownList ID="StatusDropDown" runat="server" CssClass="form-control">
                        <asp:ListItem Value="" Text="All Status"></asp:ListItem>
                        <asp:ListItem Value="Completed" Text="Completed"></asp:ListItem>
                        <asp:ListItem Value="Pending" Text="Pending"></asp:ListItem>
                        <asp:ListItem Value="Cancelled" Text="Cancelled"></asp:ListItem>
                    </asp:DropDownList>
                </div>
                <div class="form-group">
                    <label>Material Type:</label>
                    <asp:DropDownList ID="MaterialTypeDropDown" runat="server" CssClass="form-control">
                        <asp:ListItem Value="" Text="All Materials"></asp:ListItem>
                        <asp:ListItem Value="Coal" Text="Coal"></asp:ListItem>
                        <asp:ListItem Value="Sand" Text="Sand"></asp:ListItem>
                        <asp:ListItem Value="Gravel" Text="Gravel"></asp:ListItem>
                        <asp:ListItem Value="Steel" Text="Steel"></asp:ListItem>
                        <asp:ListItem Value="Other" Text="Other"></asp:ListItem>
                    </asp:DropDownList>
                </div>
                <div class="form-group">
                    <label>Min Weight (kg):</label>
                    <asp:TextBox ID="MinWeightTextBox" runat="server" CssClass="form-control" TextMode="Number" placeholder="0"></asp:TextBox>
                </div>
                <div class="form-group">
                    <label>Max Weight (kg):</label>
                    <asp:TextBox ID="MaxWeightTextBox" runat="server" CssClass="form-control" TextMode="Number" placeholder="50000"></asp:TextBox>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <asp:Button ID="SearchButton" runat="server" Text="Search" CssClass="btn btn-primary" OnClick="SearchButton_Click" />
                <asp:Button ID="ClearFiltersButton" runat="server" Text="Clear Filters" CssClass="btn btn-warning" OnClick="ClearFiltersButton_Click" />
                <asp:Button ID="ExportButton" runat="server" Text="Export to Excel" CssClass="btn btn-success" OnClick="ExportButton_Click" />
            </div>
        </div>
        
        <!-- Transactions Grid -->
        <div class="transactions-grid">
            <div class="grid-header">
                <i class="fas fa-truck"></i> Transaction Records
            </div>
            
            <asp:Panel ID="TransactionsPanel" runat="server">
                <!-- Transactions will be populated here -->
            </asp:Panel>
            
            <asp:Panel ID="EmptyStatePanel" runat="server" CssClass="empty-state" Visible="false">
                <i class="fas fa-inbox"></i>
                <h3>No Transactions Found</h3>
                <p>No transactions match your current filters. Try adjusting your search criteria.</p>
            </asp:Panel>
        </div>
        
        <!-- Pagination -->
        <div class="pagination">
            <asp:Label ID="PaginationLabel" runat="server"></asp:Label>
        </div>
    </div>
    
    <script type="text/javascript">
        // Auto-set today's date as default filter
        document.addEventListener('DOMContentLoaded', function() {
            var today = new Date().toISOString().split('T')[0];
            var fromDate = document.getElementById('<%= FromDateTextBox.ClientID %>');
            var toDate = document.getElementById('<%= ToDateTextBox.ClientID %>');
            
            if (!fromDate.value) {
                fromDate.value = today;
            }
            if (!toDate.value) {
                toDate.value = today;
            }
        });
        
        // Format numbers with commas
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        
        // Update summary displays
        function updateSummaryDisplays() {
            var totalTrans = document.getElementById('<%= TotalTransactionsLabel.ClientID %>');
            var todayTrans = document.getElementById('<%= TodayTransactionsLabel.ClientID %>');
            var totalWeight = document.getElementById('<%= TotalWeightLabel.ClientID %>');
            var avgWeight = document.getElementById('<%= AverageWeightLabel.ClientID %>');
            
            if (totalTrans) totalTrans.textContent = formatNumber(totalTrans.textContent);
            if (todayTrans) todayTrans.textContent = formatNumber(todayTrans.textContent);
            if (totalWeight) totalWeight.textContent = formatNumber(parseFloat(totalWeight.textContent).toFixed(2));
            if (avgWeight) avgWeight.textContent = formatNumber(parseFloat(avgWeight.textContent).toFixed(2));
        }
        
        // Call after page load
        setTimeout(updateSummaryDisplays, 100);
    </script>
</asp:Content>

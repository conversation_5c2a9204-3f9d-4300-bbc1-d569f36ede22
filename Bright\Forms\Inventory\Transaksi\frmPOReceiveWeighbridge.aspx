﻿<%@ Page Title="PO Receive Weighbridge" Language="vb" AutoEventWireup="false" MasterPageFile="~/Root.master" CodeBehind="frmPOReceiveWeighbridge.aspx.vb" Inherits="Bright.frmPOReceiveWeighbridge" %>

<%@ Register Src="~/View/Print/ucPrintPreview.ascx" TagPrefix="uc1" TagName="ucPrintPreview" %>
<%@ Register Src="~/View/Inventory/Transaksi/ucPOReceiveWeighbridge_list.ascx" TagPrefix="uc1" TagName="ucPOReceiveWeighbridge_list" %>
<%@ Register Src="~/View/Inventory/Transaksi/ucPOReceiveWeighbridge_edit.ascx" TagPrefix="uc1" TagName="ucPOReceiveWeighbridge_edit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="Head" runat="server">
    <title>PO Receive Weighbridge - Sistem Timbangan</title>
    <style type="text/css">
        .weighbridge-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .weighbridge-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .weighbridge-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .weighbridge-control-panel {
            border: 2px solid #4CAF50;
            border-radius: 12px;
            background: linear-gradient(145deg, #f8fffe 0%, #e8f5e8 100%);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin: 15px 0;
        }
        
        .weight-display {
            background: linear-gradient(145deg, #2c3e50, #34495e);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
            margin: 10px 0;
        }
        
        .weight-value {
            font-size: 32px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .weight-status {
            font-size: 14px;
            margin-top: 5px;
            opacity: 0.8;
        }
        
        .status-indicator {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-block;
            margin: 5px;
        }
        
        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .weighbridge-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
        }
        
        .section-header {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin: 20px 0 10px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .info-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        }
        
        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
        }
        
        .card-body {
            padding: 20px;
        }
    </style>
    
    <script type="text/javascript">
        var weighbridgeTimer;
        var currentCabangId = 0;
        var isMonitoring = false;
        var currentWeighingStep = 'idle'; // 'idle', 'weigh_in', 'weigh_out'
        
        // Initialize weighbridge on page load
        function initializeWeighbridge(cabangId) {
            currentCabangId = cabangId;
            updateStatus('Checking weighbridge connection...', 'info');
            
            $.ajax({
                url: '/api/weighbridge/status/' + cabangId,
                method: 'GET',
                success: function(data) {
                    if (data && data.isConnected) {
                        updateStatus('Weighbridge connected: ' + data.scaleName, 'success');
                    } else {
                        updateStatus('Weighbridge not connected: ' + (data.lastError || 'Unknown error'), 'warning');
                    }
                },
                error: function(xhr, status, error) {
                    updateStatus('Failed to check weighbridge status: ' + error, 'error');
                }
            });
        }
        
        // Update status display
        function updateStatus(message, type) {
            var statusDiv = document.getElementById('weighbridge-status');
            if (statusDiv) {
                statusDiv.textContent = message;
                statusDiv.className = 'status-indicator status-' + 
                    (type === 'success' ? 'connected' : 
                     type === 'error' ? 'error' : 'warning');
            }
        }
        
        // Start weight monitoring
        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            updateMonitorButton();
            
            weighbridgeTimer = setInterval(function() {
                readCurrentWeight();
            }, 2000);
            
            updateStatus('Monitoring started...', 'info');
        }
        
        // Stop weight monitoring
        function stopMonitoring() {
            if (!isMonitoring) return;
            
            isMonitoring = false;
            updateMonitorButton();
            
            if (weighbridgeTimer) {
                clearInterval(weighbridgeTimer);
                weighbridgeTimer = null;
            }
            
            updateStatus('Monitoring stopped', 'warning');
        }
        
        // Update monitor button
        function updateMonitorButton() {
            var btn = ASPxClientControl.GetControlCollection().GetByName('btn_monitor');
            if (btn) {
                btn.SetText(isMonitoring ? 'Stop Monitor' : 'Start Monitor');
            }
        }
        
        // Read current weight
        function readCurrentWeight() {
            if (currentCabangId <= 0) return;
            
            $.ajax({
                url: '/api/weighbridge/weight/' + currentCabangId,
                method: 'GET',
                success: function(data) {
                    if (data) {
                        updateWeightDisplay(data);
                        
                        if (data.isStable && currentWeighingStep !== 'idle') {
                            // Auto-save stable weight
                            saveWeight(data);
                        }
                    }
                },
                error: function() {
                    // Silent error during monitoring
                }
            });
        }
        
        // Update weight display
        function updateWeightDisplay(data) {
            var valueEl = document.getElementById('weight-value');
            var statusEl = document.getElementById('weight-status');
            var timeEl = document.getElementById('last-update');
            
            if (valueEl) {
                valueEl.textContent = data.weight + ' ' + data.unit;
            }
            
            if (statusEl) {
                statusEl.textContent = data.isStable ? 'Stable' : 'Not Stable';
                statusEl.style.color = data.isStable ? '#2ecc71' : '#f39c12';
            }
            
            if (timeEl) {
                timeEl.textContent = 'Last update: ' + new Date().toLocaleTimeString();
            }
        }
        
        // Save weight to server
        function saveWeight(data) {
            var weightData = {
                step: currentWeighingStep,
                weight: data.weight,
                unit: data.unit,
                timestamp: data.timestamp,
                scaleId: data.scaleId
            };
            
            cp_poreceiveweighbridge.PerformCallback('save_weight;' + JSON.stringify(weightData));
            
            // Stop monitoring after save
            stopMonitoring();
            currentWeighingStep = 'idle';
        }
        
        // Start weigh-in process
        function startWeighIn() {
            currentWeighingStep = 'weigh_in';
            startMonitoring();
            updateStatus('Starting weigh-in process...', 'info');
        }
        
        // Start weigh-out process
        function startWeighOut() {
            currentWeighingStep = 'weigh_out';
            startMonitoring();
            updateStatus('Starting weigh-out process...', 'info');
        }
        
        // Manual weight reading
        function readManualWeight() {
            readCurrentWeight();
        }
        
        // Test function
        function testWeight() {
            var testData = {
                weight: Math.round((Math.random() * 1000 + 500) * 100) / 100,
                unit: 'kg',
                isStable: true,
                timestamp: new Date().toISOString(),
                scaleId: 'TEST-001'
            };
            
            updateWeightDisplay(testData);
            updateStatus('Test weight: ' + testData.weight + ' kg', 'success');
        }
        
        // Initialize on page load
        $(document).ready(function() {
            // Initialize with default cabang
            var cabangId = getCurrentCabangId();
            if (cabangId > 0) {
                initializeWeighbridge(cabangId);
            }
        });
        
        // Get current cabang ID
        function getCurrentCabangId() {
            try {
                // This will be implemented in the edit control
                return 1; // Default for testing
            } catch (ex) {
                return 0;
            }
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            stopMonitoring();
        });
    </script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="LeftPanelContent" runat="server">
</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="RightPanelContent" runat="server">
</asp:Content>

<asp:Content ID="Content4" ContentPlaceHolderID="PageToolbar" runat="server">
    <div class="weighbridge-header">
        <h2><i class="fa fa-balance-scale"></i> PO Receive Weighbridge</h2>
        <p>Sistem Penerimaan Barang dengan Timbangan Real-time</p>
    </div>
</asp:Content>

<asp:Content ID="Content5" ContentPlaceHolderID="PageContent" runat="server">
    <dx:ASPxCallbackPanel ID="ASPxCallbackPanel1" runat="server" Width="100%" ClientInstanceName="cp_poreceiveweighbridge">
        <PanelCollection>
            <dx:PanelContent runat="server">
                <asp:Literal runat="server" ID="ltl_msg"></asp:Literal>
                
                <!-- Weighbridge Status Panel -->
                <div class="info-card">
                    <div class="card-header">
                        <i class="fa fa-info-circle"></i> Weighbridge Status
                    </div>
                    <div class="card-body">
                        <div id="weighbridge-status" class="status-indicator status-warning">
                            Initializing weighbridge connection...
                        </div>
                        <div id="last-update" style="font-size: 12px; color: #666; margin-top: 10px;">
                            Last update: --
                        </div>
                    </div>
                </div>
                
                <dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" AlignItemCaptionsInAllGroups="True">
                    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit">
                    </SettingsAdaptivity>
                    <Items>
                        <dx:LayoutItem ColSpan="1" Name="li_list" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucPOReceiveWeighbridge_list runat="server" ID="ucPOReceiveWeighbridge_list" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" Name="li_edit" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucPOReceiveWeighbridge_edit runat="server" id="ucPOReceiveWeighbridge_edit" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                        <dx:LayoutItem ColSpan="1" Name="li_print" ShowCaption="False">
                            <LayoutItemNestedControlCollection>
                                <dx:LayoutItemNestedControlContainer runat="server">
                                    <uc1:ucPrintPreview runat="server" ID="ucPrintPreview" />
                                </dx:LayoutItemNestedControlContainer>
                            </LayoutItemNestedControlCollection>
                        </dx:LayoutItem>
                    </Items>
                </dx:ASPxFormLayout>
            </dx:PanelContent>
        </PanelCollection>
    </dx:ASPxCallbackPanel>
</asp:Content>

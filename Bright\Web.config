﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
	<configSections>
		<sectionGroup name="devExpress">
			<section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="resources" type="DevExpress.Web.ResourcesConfigurationSection, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="bootstrap" type="DevExpress.Web.Bootstrap.BootstrapConfigurationSection, DevExpress.Web.Bootstrap.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
		</sectionGroup>
		<sectionGroup name="myviewstateSection">
			<section name="viewstateService" type="Bright.ViewStateProviderServiceSection, Bright" allowDefinition="MachineToApplication" restartOnExternalChanges="true" />
		</sectionGroup>
		<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>
	<connectionStrings>
		<add name="BrightEntities" connectionString="metadata=res://*/BrightModel.csdl|res://*/BrightModel.ssdl|res://*/BrightModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=localhost;initial catalog=Bright;persist security info=True;user id=sa;password=@masterKey123;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
		<!--<add name="BrightEntities" connectionString="metadata=res://*/BrightModel.csdl|res://*/BrightModel.ssdl|res://*/BrightModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=***************;initial catalog=Bright;persist security info=True;user id=sa;password=@masterKey123;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
		<!--<add name="BrightEntities" connectionString="metadata=res://*/BrightModel.csdl|res://*/BrightModel.ssdl|res://*/BrightModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=localhost;initial catalog=Bright_bak;persist security info=True;user id=sa;password=@masterKey123;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
	</connectionStrings>

	<!-- Application Settings -->
	<appSettings>
		<!-- Weighbridge Configuration -->
		<add key="WeighbridgeBaseUrl" value="http://127.0.0.1:8085" />
		<add key="WeighbridgeTimeout" value="5000" />
		<add key="WeighbridgeMonitorInterval" value="2000" />
		<add key="WeighbridgeRetryAttempts" value="3" />
		<add key="WeighbridgeAutoSaveStableWeight" value="true" />
	</appSettings>

	<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6.1" />
      </system.Web>
  -->
	<system.web>
		<compilation debug="true" targetFramework="4.6.1">
			<assemblies>
				<add assembly="DevExpress.Data.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.Web.ASPxThemes.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.RichEdit.v19.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.RichEdit.v19.2.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.Printing.v19.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.XtraScheduler.v19.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.Web.ASPxScheduler.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
				<add assembly="DevExpress.Web.ASPxTreeList.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.ASPxGantt.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Design, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Web.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Data.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="DevExpress.XtraReports.v19.2.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.XtraReports.v19.2.Web, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.XtraReports.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.DataAccess.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.Bootstrap.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.ASPxRichEdit.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.ASPxSpreadsheet.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Spreadsheet.v19.2.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.ASPxPivotGrid.v19.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" /></assemblies>
		</compilation>
		<authentication mode="Forms" />
		<profile>
			<providers>
				<clear />
				<add name="AspNetSqlProfileProvider" type="System.Web.Profile.SqlProfileProvider" connectionStringName="ApplicationServices" applicationName="/" />
			</providers>
		</profile>
		<roleManager enabled="false">
			<providers>
				<clear />
				<add name="AspNetSqlRoleProvider" type="System.Web.Security.SqlRoleProvider" connectionStringName="ApplicationServices" applicationName="/" />
				<add name="AspNetWindowsTokenRoleProvider" type="System.Web.Security.WindowsTokenRoleProvider" applicationName="/" />
			</providers>
		</roleManager>
		<httpHandlers>
			<add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" validate="false" />
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" validate="false" />
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" validate="false" />
		</httpHandlers>
		<httpModules>
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
			<add name="PageControllerModule" type="Bright.MVVM.PageControllerModule, Bright.MVVM" />
		</httpModules>
		<globalization culture="" uiCulture="" />
		<httpRuntime maxRequestLength="4096" requestValidationMode="4.0" executionTimeout="110" targetFramework="4.6.1" />
		<pages validateRequest="true" clientIDMode="Predictable">
			<controls>
				<add tagPrefix="dx" namespace="DevExpress.Web" assembly="DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
			</controls>
		</pages>
	</system.web>
	<system.webServer>
		<modules runAllManagedModulesForAllRequests="true">
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
			<add name="PageControllerModule" type="Bright.MVVM.PageControllerModule, Bright.MVVM" />
		</modules>
		<handlers>
			<add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" name="ASPxUploadProgressHandler" preCondition="integratedMode" />
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode" />
			<add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v19.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" name="ASPxWebDocumentViewerHandlerModule" preCondition="integratedMode" />
		<remove name="ExtensionlessUrlHandler-Integrated-4.0" /><remove name="OPTIONSVerbHandler" /><remove name="TRACEVerbHandler" /><add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" /></handlers>
		<validation validateIntegratedModeConfiguration="false" />
		<security>
			<requestFiltering>
				<requestLimits maxAllowedContentLength="30000000" />
			</requestFiltering>
		</security>
	</system.webServer>
	<devExpress>
		<resources>
			<add type="ThirdParty" />
			<add type="DevExtreme" />
		</resources>
		<themes enableThemesAssembly="true" styleSheetTheme="" theme="Office365" customThemeAssemblies="" baseColor="#4527a0" font="14px 'Segoe UI', Helvetica, 'Droid Sans', Tahoma, Geneva, sans-serif" />
		<compression enableHtmlCompression="true" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true" />
		<settings accessibilityCompliant="false" bootstrapMode="" doctypeMode="Html5" rightToLeft="false" checkReferencesToExternalScripts="true" protectControlState="true" ieCompatibilityVersion="edge" />
		<errors callbackErrorRedirectUrl="" />
		<bootstrap allowClientObjectDeferredInitialization="true" mode="Bootstrap4" iconSet="Embedded" />
	</devExpress>
	<appSettings>
		<add key="vs:EnableBrowserLink" value="false" />
	</appSettings>
	<myviewstateSection>
		<!--
           Custom viewstate provider service
     -->
		<viewstateService defaultProvider="ViewStateProviderGlobal">
			<providers>
				<add name="ViewStateProviderCache" type="Bright.ViewStateProviderCache" />
				<add name="ViewStateProviderGlobal" type="Bright.ViewStateProviderGlobal" />
				<add name="ViewStateProviderSession" type="Bright.ViewStateProviderSession" />
			</providers>
		</viewstateService>
	</myviewstateSection>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework" />
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
		</providers>
	</entityFramework>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>

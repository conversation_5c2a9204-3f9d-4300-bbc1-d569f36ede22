Imports System.Web.Http
Imports System.Web.Http.Description
Imports System.Web.Http.Routing
Imports Bright.Helpers
Imports Bright.Domain
Imports Newtonsoft.Json

<System.Web.Http.RoutePrefixAttribute("api/weighbridge")>
Public Class WeighBridgeApiController
    Inherits ApiController

    <HttpGet>
    <System.Web.Http.RouteAttribute("status/{cabangId:int}")>
    Public Function GetStatus(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim config = manager.GetConfiguration(cabangId)
            
            If config Is Nothing Then
                Return BadRequest("Konfigurasi weighbridge tidak ditemukan")
            End If
            
            Dim result = New With {
                .cabangId = cabangId,
                .scaleName = config.ScaleName,
                .connectionType = config.ConnectionType,
                .isConfigured = True,
                .isConnected = False,
                .lastError = ""
            }
            
            Try
                Dim service = manager.GetService(cabangId)
                result = New With {
                    .cabangId = cabangId,
                    .scaleName = config.ScaleName,
                    .connectionType = config.ConnectionType,
                    .isConfigured = True,
                    .isConnected = service.IsConnected(),
                    .lastError = service.LastError
                }
            Catch ex As Exception
                result = New With {
                    .cabangId = cabangId,
                    .scaleName = config.ScaleName,
                    .connectionType = config.ConnectionType,
                    .isConfigured = True,
                    .isConnected = False,
                    .lastError = ex.Message
                }
            End Try
            
            Return Ok(result)
            
        Catch ex As Exception
            Return InternalServerError(ex)
        End Try
    End Function

    <HttpGet>
    <Route("weight/{cabangId:int}")>
    Public Function GetWeight(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim service = manager.GetService(cabangId)
            
            If Not service.IsConnected() Then
                Return BadRequest("Weighbridge tidak terhubung")
            End If
            
            Dim weightData = service.GetWeight()
            
            Dim result = New With {
                .weight = weightData.Weight,
                .unit = weightData.Unit,
                .timestamp = weightData.Timestamp,
                .isStable = weightData.IsStable,
                .scaleId = weightData.ScaleId,
                .rawData = weightData.RawData
            }
            
            Return Ok(result)
            
        Catch ex As Exception
            Return InternalServerError(ex)
        End Try
    End Function

    <HttpPost>
    <Route("connect/{cabangId:int}")>
    Public Function Connect(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim service = manager.GetService(cabangId)
            
            Dim result = New With {
                .success = service.IsConnected(),
                .connectionType = service.ConnectionType,
                .message = If(service.IsConnected(), "Berhasil terhubung", service.LastError)
            }
            
            Return Ok(result)
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .connectionType = "",
                .message = ex.Message
            })
        End Try
    End Function

    <HttpPost>
    <Route("disconnect/{cabangId:int}")>
    Public Function Disconnect(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            manager.DisconnectService(cabangId)
            
            Return Ok(New With {
                .success = True,
                .message = "Berhasil memutus koneksi"
            })
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .message = ex.Message
            })
        End Try
    End Function

    <HttpPost>
    <Route("test/{configId:int}")>
    Public Function TestConnection(configId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim success = manager.TestConnection(configId)
            
            Return Ok(New With {
                .success = success,
                .message = If(success, "Test koneksi berhasil", "Test koneksi gagal")
            })
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .message = ex.Message
            })
        End Try
    End Function

    ' Dummy-specific endpoints for development
    <HttpPost>
    <Route("dummy/weigh-in/{cabangId:int}")>
    Public Function SimulateWeighIn(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            manager.SimulateWeighIn(cabangId)
            
            Return Ok(New With {
                .success = True,
                .message = "Simulasi timbang masuk dimulai"
            })
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .message = ex.Message
            })
        End Try
    End Function

    <HttpPost>
    <Route("dummy/weigh-out/{cabangId:int}")>
    Public Function SimulateWeighOut(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            manager.SimulateWeighOut(cabangId)
            
            Return Ok(New With {
                .success = True,
                .message = "Simulasi timbang keluar dimulai"
            })
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .message = ex.Message
            })
        End Try
    End Function

    <HttpPost>
    <Route("dummy/set-weight/{cabangId:int}")>
    Public Function SetDummyWeight(cabangId As Integer, <FromBody> data As Object) As IHttpActionResult
        Try
            Dim json = JsonConvert.SerializeObject(data)
            Dim weightData = JsonConvert.DeserializeAnonymousType(json, New With {.weight = 0D})
            
            Dim manager = WeighBridgeManager.Instance
            manager.SetDummyWeight(cabangId, CDec(weightData.weight))
            
            Return Ok(New With {
                .success = True,
                .message = $"Berat dummy diset ke {weightData.weight} KG"
            })
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .message = ex.Message
            })
        End Try
    End Function

    <HttpGet>
    <Route("dummy/status/{cabangId:int}")>
    Public Function GetDummyStatus(cabangId As Integer) As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim dummyService = manager.GetDummyService(cabangId)
            
            Return Ok(New With {
                .success = True,
                .status = dummyService.GetSimulationStatus(),
                .isConnected = dummyService.IsConnected()
            })
            
        Catch ex As Exception
            Return Ok(New With {
                .success = False,
                .message = ex.Message
            })
        End Try
    End Function

    <HttpGet>
    <Route("active-services")>
    Public Function GetActiveServices() As IHttpActionResult
        Try
            Dim manager = WeighBridgeManager.Instance
            Dim services = manager.GetActiveServices()
            
            Return Ok(New With {
                .success = True,
                .services = services
            })
            
        Catch ex As Exception
            Return InternalServerError(ex)
        End Try
    End Function

End Class

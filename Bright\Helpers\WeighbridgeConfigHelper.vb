Imports System.Configuration
Imports System.Web

Namespace Bright.Helpers
    ''' <summary>
    ''' Helper class untuk membaca konfigurasi weighbridge dari web.config
    ''' </summary>
    Public Class WeighbridgeConfigHelper

        ''' <summary>
        ''' Base URL untuk weighbridge client API
        ''' Default: http://127.0.0.1:8085
        ''' </summary>
        Public Shared ReadOnly Property BaseUrl As String
            Get
                Return ConfigurationManager.AppSettings("WeighbridgeBaseUrl") ?? "http://127.0.0.1:8085"
            End Get
        End Property

        ''' <summary>
        ''' Timeout untuk API calls dalam milliseconds
        ''' Default: 5000 (5 detik)
        ''' </summary>
        Public Shared ReadOnly Property Timeout As Integer
            Get
                Dim timeoutStr = ConfigurationManager.AppSettings("WeighbridgeTimeout")
                Dim timeout As Integer
                If Integer.TryParse(timeoutStr, timeout) Then
                    Return timeout
                End If
                Return 5000 ' Default 5 seconds
            End Get
        End Property

        ''' <summary>
        ''' Interval monitoring dalam milliseconds
        ''' Default: 2000 (2 detik)
        ''' </summary>
        Public Shared ReadOnly Property MonitorInterval As Integer
            Get
                Dim intervalStr = ConfigurationManager.AppSettings("WeighbridgeMonitorInterval")
                Dim interval As Integer
                If Integer.TryParse(intervalStr, interval) Then
                    Return interval
                End If
                Return 2000 ' Default 2 seconds
            End Get
        End Property

        ''' <summary>
        ''' Jumlah retry attempts untuk API calls
        ''' Default: 3
        ''' </summary>
        Public Shared ReadOnly Property RetryAttempts As Integer
            Get
                Dim retryStr = ConfigurationManager.AppSettings("WeighbridgeRetryAttempts")
                Dim retry As Integer
                If Integer.TryParse(retryStr, retry) Then
                    Return retry
                End If
                Return 3 ' Default 3 attempts
            End Get
        End Property

        ''' <summary>
        ''' Auto save stable weight during weighing process
        ''' Default: true
        ''' </summary>
        Public Shared ReadOnly Property AutoSaveStableWeight As Boolean
            Get
                Dim autoSaveStr = ConfigurationManager.AppSettings("WeighbridgeAutoSaveStableWeight")
                Dim autoSave As Boolean
                If Boolean.TryParse(autoSaveStr, autoSave) Then
                    Return autoSave
                End If
                Return True ' Default true
            End Get
        End Property

        ''' <summary>
        ''' Get current weight endpoint URL
        ''' </summary>
        Public Shared ReadOnly Property GetCurrentWeightUrl As String
            Get
                Return $"{BaseUrl}/api/scale/GetCurrentWeight"
            End Get
        End Property

        ''' <summary>
        ''' Get status endpoint URL
        ''' </summary>
        Public Shared ReadOnly Property GetStatusUrl As String
            Get
                Return $"{BaseUrl}/api/scale/GetStatus"
            End Get
        End Property

        ''' <summary>
        ''' Get all weighbridge configuration as JSON for JavaScript
        ''' </summary>
        Public Shared Function GetConfigurationJson() As String
            Dim config = New With {
                .baseUrl = BaseUrl,
                .timeout = Timeout,
                .monitorInterval = MonitorInterval,
                .retryAttempts = RetryAttempts,
                .autoSaveStableWeight = AutoSaveStableWeight,
                .endpoints = New With {
                    .getCurrentWeight = GetCurrentWeightUrl,
                    .getStatus = GetStatusUrl
                }
            }

            Return Newtonsoft.Json.JsonConvert.SerializeObject(config)
        End Function

        ''' <summary>
        ''' Validate weighbridge configuration
        ''' </summary>
        Public Shared Function ValidateConfiguration() As List(Of String)
            Dim errors As New List(Of String)

            ' Validate Base URL
            If String.IsNullOrWhiteSpace(BaseUrl) Then
                errors.Add("WeighbridgeBaseUrl is required")
            ElseIf Not Uri.IsWellFormedUriString(BaseUrl, UriKind.Absolute) Then
                errors.Add("WeighbridgeBaseUrl is not a valid URL")
            End If

            ' Validate Timeout
            If Timeout <= 0 OrElse Timeout > 60000 Then
                errors.Add("WeighbridgeTimeout must be between 1 and 60000 milliseconds")
            End If

            ' Validate Monitor Interval
            If MonitorInterval <= 0 OrElse MonitorInterval > 30000 Then
                errors.Add("WeighbridgeMonitorInterval must be between 1 and 30000 milliseconds")
            End If

            ' Validate Retry Attempts
            If RetryAttempts < 0 OrElse RetryAttempts > 10 Then
                errors.Add("WeighbridgeRetryAttempts must be between 0 and 10")
            End If

            Return errors
        End Function

        ''' <summary>
        ''' Get configuration summary for logging/debugging
        ''' </summary>
        Public Shared Function GetConfigurationSummary() As String
            Return $"Weighbridge Config - BaseUrl: {BaseUrl}, Timeout: {Timeout}ms, Interval: {MonitorInterval}ms, Retry: {RetryAttempts}, AutoSave: {AutoSaveStableWeight}"
        End Function

    End Class
End Namespace

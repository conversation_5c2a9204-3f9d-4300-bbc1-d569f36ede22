<%@ Page Title="Weighbridge System" Language="VB" MasterPageFile="~/Root.master" AutoEventWireup="false" CodeBehind="WeighbridgeMain.aspx.vb" Inherits="Bright.WeighbridgeMain" Async="true" %>

<asp:Content ID="Content1" ContentPlaceHolderID="Head" runat="server">
    <style>
        .weighbridge-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .weight-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .weight-value {
            font-size: 4rem;
            font-weight: bold;
            margin: 10px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .weight-unit {
            font-size: 1.5rem;
            opacity: 0.9;
        }
        
        .status-indicator {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-stable {
            background-color: #28a745;
            animation: pulse-stable 2s infinite;
        }
        
        .status-unstable {
            background-color: #ffc107;
            animation: pulse-unstable 1s infinite;
        }
        
        .status-disconnected {
            background-color: #dc3545;
            animation: pulse-error 0.5s infinite;
        }
        
        @keyframes pulse-stable {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }
        
        @keyframes pulse-unstable {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.4; }
        }
        
        @keyframes pulse-error {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.2; }
        }
        
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .info-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1rem;
        }
        
        .info-card p {
            margin: 5px 0;
            color: #666;
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        .log-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .timestamp {
            color: #6c757d;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 8px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeeba;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
    
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="PageContent" runat="server">
    <div class="weighbridge-container">
        <!-- Header -->
        <h1 style="text-align: center; color: #333; margin-bottom: 30px;">
            <i class="fas fa-weight"></i> Weighbridge System
        </h1>
        
        <!-- Alert Panel -->
        <asp:Panel ID="AlertPanel" runat="server" Visible="false">
            <div id="alertMessage" class="alert">
                <asp:Label ID="AlertLabel" runat="server"></asp:Label>
            </div>
        </asp:Panel>
        
        <!-- Weight Display -->
        <div class="weight-display">
            <div>
                <span id="statusIndicator" class="status-indicator status-disconnected"></span>
                <span id="connectionStatus">Connecting...</span>
            </div>
            <div class="weight-value" id="weightValue">
                <asp:Label ID="WeightLabel" runat="server" Text="0.00"></asp:Label>
            </div>
            <div class="weight-unit">Kilograms</div>
            <div style="margin-top: 15px;">
                <asp:Label ID="StabilityLabel" runat="server" Text="Checking stability..."></asp:Label>
            </div>
            <div style="margin-top: 10px; font-size: 0.9rem;">
                Last Update: <span id="lastUpdate">Never</span>
            </div>        </div>
        
        <!-- Truck Transaction Section -->
        <div class="info-cards">
            <div class="info-card" style="grid-column: 1 / -1;">
                <h3><i class="fas fa-truck"></i> Pencatatan Truk</h3>
                
                <!-- Tab Navigation -->
                <div style="margin-bottom: 20px;">
                    <button type="button" id="tabTruckEntry" class="btn btn-primary" onclick="showTab('entry')" style="margin-right: 10px;">Truk Masuk</button>
                    <button type="button" id="tabTruckExit" class="btn btn-secondary" onclick="showTab('exit')">Truk Keluar</button>
                    <button type="button" id="tabTruckHistory" class="btn btn-secondary" onclick="showTab('history')">History</button>
                </div>
                  <!-- Truk Masuk Form -->
                <div id="truckEntryForm" style="display: block;">
                    <h4>Data Truk Masuk (Gross Weight)</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label>Kendaraan:</label>
                            <div style="display: flex; gap: 5px; align-items: center;">
                                <asp:DropDownList ID="VehicleDropdownEntry" runat="server" CssClass="form-control" 
                                    style="width: 85%;" AppendDataBoundItems="true" DataTextField="DisplayText" DataValueField="Id">
                                    <asp:ListItem Text="-- Pilih Kendaraan --" Value="" />
                                </asp:DropDownList>
                                <button type="button" class="btn btn-sm btn-primary" onclick="showAddVehicleModal()" 
                                    style="width: 15%; font-size: 16px;" title="Tambah Kendaraan Baru">+</button>
                            </div>
                        </div>
                        <div>
                            <label>Nama Driver:</label>
                            <asp:TextBox ID="DriverNameEntry" runat="server" CssClass="form-control" placeholder="Nama lengkap driver" readonly />
                        </div>
                        <div>
                            <label>Perusahaan:</label>
                            <asp:TextBox ID="CompanyEntry" runat="server" CssClass="form-control" placeholder="Nama perusahaan" />
                        </div>
                        <div>
                            <label>Material:</label>
                            <asp:TextBox ID="MaterialEntry" runat="server" CssClass="form-control" placeholder="Jenis material" />
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label>Keterangan:</label>
                            <asp:TextBox ID="RemarksEntry" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" placeholder="Catatan tambahan..." />
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <button type="button" id="btnSaveTruckEntry" class="btn btn-success" onclick="saveTruckEntry()">
                            <i class="fas fa-save"></i> Simpan Truk Masuk
                        </button>
                        <span id="currentWeightEntry" style="margin-left: 15px; color: #666;">Berat saat ini: <strong>0.00 kg</strong></span>
                    </div>
                </div>
                  <!-- Truk Keluar Form -->
                <div id="truckExitForm" style="display: none;">
                    <h4>Data Truk Keluar (Tare Weight)</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label>Kendaraan:</label>
                            <div style="display: flex; gap: 5px; align-items: center;">
                                <asp:DropDownList ID="VehicleDropdownExit" runat="server" CssClass="form-control" 
                                    style="width: 85%;" AppendDataBoundItems="true" DataTextField="DisplayText" DataValueField="Id">
                                    <asp:ListItem Text="-- Pilih Kendaraan --" Value="" />
                                </asp:DropDownList>
                                <button type="button" onclick="searchActiveTruck()" class="btn btn-sm btn-info" 
                                    style="width: 15%;" title="Cari Truk Aktif">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label>Status Truk:</label>
                            <div id="truckStatus" style="padding: 8px; background: #f8f9fa; border-radius: 4px; margin-top: 5px;">
                                Belum ada data
                            </div>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <div id="truckDetails" style="display: none; background: #e9ecef; padding: 15px; border-radius: 4px; margin: 10px 0;">
                                <h5>Detail Truk Masuk:</h5>
                                <div id="truckDetailContent"></div>
                            </div>
                        </div>
                        <div style="grid-column: 1 / -1;">
                            <label>Keterangan Keluar:</label>
                            <asp:TextBox ID="RemarksExit" runat="server" CssClass="form-control" TextMode="MultiLine" Rows="2" placeholder="Catatan untuk truk keluar..." />
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <button type="button" id="btnSaveTruckExit" class="btn btn-warning" onclick="saveTruckExit()" disabled>
                            <i class="fas fa-sign-out-alt"></i> Simpan Truk Keluar
                        </button>
                        <span id="currentWeightExit" style="margin-left: 15px; color: #666;">Berat saat ini: <strong>0.00 kg</strong></span>
                    </div>
                </div>
                
                <!-- History Table -->
                <div id="truckHistoryTable" style="display: none;">
                    <h4>History Transaksi Hari Ini</h4>
                    <div style="overflow-x: auto;">
                        <table class="table table-striped" style="width: 100%; border-collapse: collapse;">
                            <thead style="background: #f8f9fa;">
                                <tr>
                                    <th style="padding: 10px; border: 1px solid #ddd;">No Truk</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Driver</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Material</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Masuk</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Keluar</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Gross (kg)</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Tare (kg)</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Netto (kg)</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">Status</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <tr>
                                    <td colspan="9" style="padding: 20px; text-align: center; color: #666;">Memuat data...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="margin-top: 15px;">
                        <button type="button" onclick="refreshHistory()" class="btn btn-primary">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                        <button type="button" onclick="exportToExcel()" class="btn btn-success">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Information Cards -->
        <div class="info-cards">
        <div class="info-cards">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> Service Information</h3>
                <p><strong>API Endpoint:</strong> <span id="apiEndpoint">http://127.0.0.1:8085</span></p>
                <p><strong>Status:</strong> <span id="serviceStatus">Checking...</span></p>
                <p><strong>Connection Type:</strong> <span id="connectionType">Unknown</span></p>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-cogs"></i> Scale Configuration</h3>
                <p><strong>Port:</strong> <span id="scalePort">COM1</span></p>
                <p><strong>Baud Rate:</strong> <span id="baudRate">9600</span></p>
                <p><strong>Timeout:</strong> <span id="timeout">500ms</span></p>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-chart-line"></i> Statistics</h3>
                <p><strong>Total Readings:</strong> <span id="totalReadings">0</span></p>
                <p><strong>Stable Readings:</strong> <span id="stableReadings">0</span></p>
                <p><strong>Success Rate:</strong> <span id="successRate">0%</span></p>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-clock"></i> Session Info</h3>
                <p><strong>Session Start:</strong> <span id="sessionStart">Not started</span></p>
                <p><strong>Uptime:</strong> <span id="uptime">0 minutes</span></p>
                <p><strong>Last Error:</strong> <span id="lastError">None</span></p>
            </div>
        </div>
        
        <!-- Control Panel -->
        <div class="control-panel">            <asp:Button ID="ConnectButton" runat="server" Text="Connect to Service" CssClass="btn btn-primary" OnClientClick="connectToService(); return false;" />
            <asp:Button ID="DisconnectButton" runat="server" Text="Disconnect" CssClass="btn btn-warning" OnClientClick="disconnectFromService(); return false;" Enabled="false" />
            <asp:Button ID="TestConnectionButton" runat="server" Text="Test Connection" CssClass="btn btn-success" OnClientClick="testConnection(); return false;" />
            <asp:Button ID="RefreshButton" runat="server" Text="Refresh Data" CssClass="btn btn-primary" OnClientClick="refreshWeightData(); return false;" />            <asp:Button ID="ZeroScaleButton" runat="server" Text="Zero Scale" CssClass="btn btn-warning" OnClientClick="showClientAlert('Zero scale command sent (functionality to be implemented)', 'warning'); addClientLogEntry('User', 'Zero scale command initiated'); return false;" />
            <asp:Button ID="ClearLogButton" runat="server" Text="Clear Log" CssClass="btn btn-danger" OnClientClick="clearClientLog(); return false;" />
        </div>
        
        <!-- Settings Panel -->
        <div class="info-card" style="margin-bottom: 30px;">
            <h3><i class="fas fa-settings"></i> Quick Settings</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 15px;">
                <div>
                    <label for="apiUrlInput">API URL:</label>
                    <asp:TextBox ID="ApiUrlTextBox" runat="server" Text="http://127.0.0.1:8085" CssClass="form-control" style="margin-top: 5px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 100%;"></asp:TextBox>
                </div>
                <div>
                    <label for="refreshIntervalInput">Refresh Interval (ms):</label>
                    <asp:TextBox ID="RefreshIntervalTextBox" runat="server" Text="1000" CssClass="form-control" style="margin-top: 5px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 100%;"></asp:TextBox>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <asp:Button ID="SaveSettingsButton" runat="server" Text="Save Settings" CssClass="btn btn-success" OnClientClick="saveClientSettings(); return false;" />
                <asp:CheckBox ID="AutoRefreshCheckBox" runat="server" Text="Auto Refresh" Checked="true" style="margin-left: 20px;" />
            </div>        </div>
        
        <!-- Truck Transaction Section -->
        <div class="info-card">
            <h3><i class="fas fa-truck"></i> Transaksi Truk</h3>
            
            <!-- Truck Entry Form -->
            <div style="margin-bottom: 20px;">
                <h4>Truk Masuk</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                    <input type="text" id="truckNumber" placeholder="Nomor Truk" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    <input type="text" id="driverName" placeholder="Nama Driver" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    <input type="text" id="company" placeholder="Perusahaan" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                    <input type="text" id="material" placeholder="Jenis Muatan" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;" />
                </div>
                <button onclick="truckCheckIn()" class="btn btn-success" style="width: 100%;">
                    <i class="fas fa-sign-in-alt"></i> Check-In Truk (Berat Kotor)
                </button>
            </div>
            
            <!-- Active Trucks -->
            <div style="margin-bottom: 20px;">
                <h4>Truk Aktif (Dalam Area)</h4>
                <div id="activeTrucks" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                    <div style="color: #666; text-align: center;">Tidak ada truk aktif</div>
                </div>
            </div>
            
            <!-- Transaction History -->
            <div>
                <h4>Riwayat Transaksi (5 Terakhir)</h4>
                <div id="transactionHistory" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                    <div style="color: #666; text-align: center;">Belum ada transaksi</div>
                </div>
            </div>
        </div>
        
        <!-- Activity Log -->
        <div class="info-card">
            <h3><i class="fas fa-list"></i> Activity Log</h3>
            <div class="log-panel">
                <asp:Panel ID="LogPanel" runat="server">
                    <div class="log-entry">
                        <span class="timestamp">[System]</span> Weighbridge interface initialized
                    </div>
                </asp:Panel>
            </div>
        </div>
    </div>
      <!-- Add Vehicle Modal -->
    <div class="modal fade" id="addVehicleModal" tabindex="-1" role="dialog" aria-labelledby="addVehicleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addVehicleModalLabel">Tambah Kendaraan Baru</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addVehicleForm">
                        <div class="form-group">
                            <label for="newVehiclePlate">No. Polisi *</label>
                            <input type="text" class="form-control" id="newVehiclePlate" required style="text-transform: uppercase;">
                        </div>
                        <div class="form-group">
                            <label for="newVehicleName">Nama Kendaraan</label>
                            <input type="text" class="form-control" id="newVehicleName" placeholder="Contoh: Truck Fuso">
                        </div>
                        <div class="form-group">
                            <label for="newVehicleType">Jenis Kendaraan</label>
                            <input type="text" class="form-control" id="newVehicleType" placeholder="Contoh: Truck, Pickup, dll">
                        </div>
                        <div class="form-group">
                            <label for="newDriverName">Nama Sopir</label>
                            <input type="text" class="form-control" id="newDriverName" placeholder="Nama lengkap sopir">
                        </div>
                        <div class="form-group">
                            <label for="newDriverPhone">No. HP</label>
                            <input type="text" class="form-control" id="newDriverPhone" placeholder="Nomor HP sopir">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewVehicle()">Simpan</button>
                </div>
            </div>
        </div>
    </div>
      <!-- Auto-refresh script -->
    <script type="text/javascript">
        var autoRefreshEnabled = true;
        var refreshInterval = 1000;
        var sessionStartTime = new Date();
        var totalReadings = 0;
        var stableReadings = 0;
        var isConnected = false;
        var apiBaseUrl = 'http://127.0.0.1:8085';
        
        function updateUptime() {
            var now = new Date();
            var diff = Math.floor((now - sessionStartTime) / 1000 / 60);
            document.getElementById('uptime').textContent = diff + ' minutes';
        }
        
        function updateSessionInfo() {
            document.getElementById('sessionStart').textContent = sessionStartTime.toLocaleString();
            updateUptime();
            
            if (totalReadings > 0) {
                var rate = Math.floor((stableReadings / totalReadings) * 100);
                document.getElementById('successRate').textContent = rate + '%';
            }
            
            document.getElementById('totalReadings').textContent = totalReadings;
            document.getElementById('stableReadings').textContent = stableReadings;
        }
        
        // Client-side API calls - no postback!
        async function performAutoRefresh() {
            if (autoRefreshEnabled && isConnected) {
                await refreshWeightData();
            }
        }
        
        async function refreshWeightData() {
            try {
                // Get current weight
                const weightResponse = await fetch(apiBaseUrl + '/api/scale/GetCurrentWeight');
                if (weightResponse.ok) {
                    const weightData = await weightResponse.json();
                    
                    // Update display elements
                    document.getElementById('<%= WeightLabel.ClientID %>').textContent = weightData.Weight.toFixed(2);
                    document.getElementById('<%= StabilityLabel.ClientID %>').textContent = weightData.IsStable ? 'Stable ✓' : 'Unstable ⚠';
                    
                    // Update status indicator
                    const statusClass = weightData.IsStable ? 'status-stable' : 'status-unstable';
                    document.getElementById('statusIndicator').className = 'status-indicator ' + statusClass;
                    document.getElementById('connectionStatus').textContent = weightData.IsStable ? 'Stable' : 'Reading...';
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
                      // Update counters
                    totalReadings++;
                    if (weightData.IsStable) {
                        stableReadings++;
                    }
                    
                    // Update truck form weight displays
                    updateCurrentWeightDisplays();
                    
                    // Add log entry (client-side)
                    addClientLogEntry('Data', `Weight: ${weightData.Weight.toFixed(2)} kg, Stable: ${weightData.IsStable}`);
                }
                
                // Get scale status
                const statusResponse = await fetch(apiBaseUrl + '/api/scale/GetStatus');
                if (statusResponse.ok) {
                    const statusData = await statusResponse.json();
                    document.getElementById('scalePort').textContent = statusData.PortName;
                    document.getElementById('connectionType').textContent = statusData.ConnectionType;
                }
                
            } catch (error) {
                console.error('Error refreshing data:', error);
                addClientLogEntry('Error', 'Failed to refresh data: ' + error.message);
            }
        }
        
        async function connectToService() {
            try {
                showClientAlert('Connecting to WeighbridgeService...', 'warning');
                addClientLogEntry('User', 'Attempting to connect to service');
                
                const response = await fetch(apiBaseUrl + '/api/scale/TestConnection');
                if (response.ok) {
                    isConnected = true;
                    document.getElementById('<%= ConnectButton.ClientID %>').disabled = true;
                    document.getElementById('<%= DisconnectButton.ClientID %>').disabled = false;
                    showClientAlert('Successfully connected to WeighbridgeService!', 'success');
                    addClientLogEntry('System', 'Connected to service at ' + apiBaseUrl);
                    
                    // Load initial data
                    await refreshWeightData();
                } else {
                    showClientAlert('Failed to connect to WeighbridgeService. Please check if the service is running.', 'danger');
                    addClientLogEntry('Error', 'Connection failed');
                }
            } catch (error) {
                showClientAlert('Connection error: ' + error.message, 'danger');
                addClientLogEntry('Error', 'Connection error: ' + error.message);
            }
        }
        
        function disconnectFromService() {
            isConnected = false;
            document.getElementById('<%= ConnectButton.ClientID %>').disabled = false;
            document.getElementById('<%= DisconnectButton.ClientID %>').disabled = true;
            showClientAlert('Disconnected from WeighbridgeService', 'info');
            addClientLogEntry('User', 'Disconnected from service');
        }
        
        async function testConnection() {
            try {
                addClientLogEntry('User', 'Testing connection to service');
                
                const response = await fetch(apiBaseUrl + '/api/scale/TestConnection');
                if (response.ok) {
                    const result = await response.text();
                    showClientAlert('Connection test successful! ' + result, 'success');
                    addClientLogEntry('Test', 'Connection test passed');
                } else {
                    showClientAlert('Connection test failed. Status: ' + response.status, 'warning');
                    addClientLogEntry('Test', 'Connection test failed: ' + response.status);
                }
            } catch (error) {
                showClientAlert('Connection test error: ' + error.message, 'danger');
                addClientLogEntry('Error', 'Connection test error: ' + error.message);
            }
        }
        
        function showClientAlert(message, type) {
            // Create or update alert element
            let alertDiv = document.getElementById('clientAlert');
            if (!alertDiv) {
                alertDiv = document.createElement('div');
                alertDiv.id = 'clientAlert';
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 15px; border-radius: 5px; max-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);';
                document.body.appendChild(alertDiv);
            }
            
            // Set alert styling based on type
            const colors = {
                'success': { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
                'warning': { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
                'danger': { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
                'info': { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
            };
            
            const color = colors[type] || colors['info'];
            alertDiv.style.backgroundColor = color.bg;
            alertDiv.style.borderLeft = '4px solid ' + color.border;
            alertDiv.style.color = color.text;
            alertDiv.textContent = message;
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (alertDiv && alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
        
        function addClientLogEntry(type, message) {
            const logPanel = document.querySelector('.log-panel');
            if (logPanel) {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `<span class="timestamp">[${type}]</span> ${message}`;
                
                // Add to top of log
                const firstChild = logPanel.firstChild;
                if (firstChild) {
                    logPanel.insertBefore(logEntry, firstChild);
                } else {
                    logPanel.appendChild(logEntry);
                }
                
                // Keep only last 10 entries
                const entries = logPanel.querySelectorAll('.log-entry');
                if (entries.length > 10) {
                    for (let i = 10; i < entries.length; i++) {
                        entries[i].remove();
                    }
                }
            }
        }
        
        // Update session info every 30 seconds
        setInterval(updateSessionInfo, 30000);        function clearClientLog() {
            const logPanel = document.querySelector('.log-panel');
            if (logPanel) {
                logPanel.innerHTML = '<div class="log-entry"><span class="timestamp">[System]</span> Log cleared</div>';
            }
            showClientAlert('Activity log cleared', 'info');
        }
        
        function saveClientSettings() {
            try {
                // Get values from form
                const newApiUrl = document.getElementById('<%= ApiUrlTextBox.ClientID %>').value;
                const newRefreshInterval = parseInt(document.getElementById('<%= RefreshIntervalTextBox.ClientID %>').value);
                
                // Validate
                if (!newApiUrl) {
                    showClientAlert('Please enter a valid API URL', 'danger');
                    return;
                }
                
                if (isNaN(newRefreshInterval) || newRefreshInterval < 500) {
                    showClientAlert('Refresh interval must be at least 500ms', 'danger');
                    return;
                }
                
                // Update settings
                apiBaseUrl = newApiUrl;
                refreshInterval = newRefreshInterval;
                
                // Store in localStorage
                localStorage.setItem('weighbridge_apiUrl', apiBaseUrl);
                localStorage.setItem('weighbridge_refreshInterval', refreshInterval);
                
                showClientAlert('Settings saved successfully!', 'success');
                addClientLogEntry('System', 'Settings updated: API=' + apiBaseUrl + ', Interval=' + refreshInterval + 'ms');
                
                // Update the display
                document.getElementById('apiEndpoint').textContent = apiBaseUrl;
                
            } catch (error) {
                showClientAlert('Error saving settings: ' + error.message, 'danger');
            }
        }
          function loadClientSettings() {
            try {
                // Load from localStorage
                const savedApiUrl = localStorage.getItem('weighbridge_apiUrl');
                const savedInterval = localStorage.getItem('weighbridge_refreshInterval');
                
                if (savedApiUrl) {
                    apiBaseUrl = savedApiUrl;
                    document.getElementById('<%= ApiUrlTextBox.ClientID %>').value = savedApiUrl;
                    document.getElementById('apiEndpoint').textContent = savedApiUrl;
                }
                
                if (savedInterval) {
                    refreshInterval = parseInt(savedInterval);
                    document.getElementById('<%= RefreshIntervalTextBox.ClientID %>').value = savedInterval;
                }
            } catch (error) {
                console.warn('Could not load saved settings:', error);
            }
        }
        
        // Truck Management Functions
        function showTab(tabName) {
            // Hide all tabs
            document.getElementById('truckEntryForm').style.display = 'none';
            document.getElementById('truckExitForm').style.display = 'none';
            document.getElementById('truckHistoryTable').style.display = 'none';
            
            // Reset button styles
            document.getElementById('tabTruckEntry').className = 'btn btn-secondary';
            document.getElementById('tabTruckExit').className = 'btn btn-secondary';
            document.getElementById('tabTruckHistory').className = 'btn btn-secondary';
            
            // Show selected tab
            if (tabName === 'entry') {
                document.getElementById('truckEntryForm').style.display = 'block';
                document.getElementById('tabTruckEntry').className = 'btn btn-primary';
            } else if (tabName === 'exit') {
                document.getElementById('truckExitForm').style.display = 'block';
                document.getElementById('tabTruckExit').className = 'btn btn-primary';
            } else if (tabName === 'history') {
                document.getElementById('truckHistoryTable').style.display = 'block';
                document.getElementById('tabTruckHistory').className = 'btn btn-primary';
                refreshHistory();
            }
        }
          async function saveTruckEntry() {
            try {
                // Get current weight first
                const weightResponse = await fetch(apiBaseUrl + '/api/scale/GetCurrentWeight');
                if (!weightResponse.ok) {
                    showClientAlert('Tidak dapat membaca berat timbangan!', 'danger');
                    return;
                }
                
                const weightData = await weightResponse.json();
                if (!weightData.IsStable) {
                    showClientAlert('Berat timbangan belum stabil! Tunggu hingga stabil.', 'warning');
                    return;
                }
                
                // Get form data
                const vehicleDropdown = document.getElementById('<%= VehicleDropdownEntry.ClientID %>');
                const vehicleId = vehicleDropdown.value;
                const driverName = document.getElementById('<%= DriverNameEntry.ClientID %>').value.trim();
                const company = document.getElementById('<%= CompanyEntry.ClientID %>').value.trim();
                const material = document.getElementById('<%= MaterialEntry.ClientID %>').value.trim();
                const remarks = document.getElementById('<%= RemarksEntry.ClientID %>').value.trim();
                
                // Validate
                if (!vehicleId || vehicleId === '' || !company || !material) {
                    showClientAlert('Kendaraan, perusahaan, dan material wajib diisi!', 'danger');
                    return;
                }
                
                // Prepare data
                const entryData = {
                    vehicleId: parseInt(vehicleId),
                    driverName: driverName,
                    company: company,
                    material: material,
                    remarks: remarks,
                    grossWeight: weightData.Weight,
                    operatorName: 'Current User'
                };
                
                // Call server-side method (we'll add this)
                __doPostBack('<%= Page.ClientID %>', 'SAVE_TRUCK_ENTRY:' + JSON.stringify(entryData));
                
            } catch (error) {
                showClientAlert('Error: ' + error.message, 'danger');
            }
        }
          async function searchActiveTruck() {
            const vehicleDropdown = document.getElementById('<%= VehicleDropdownExit.ClientID %>');
            const vehicleId = vehicleDropdown.value;
            if (!vehicleId || vehicleId === '') {
                showClientAlert('Pilih kendaraan terlebih dahulu!', 'warning');
                return;
            }
            
            // Call server-side method to search truck
            __doPostBack('<%= Page.ClientID %>', 'SEARCH_TRUCK:' + vehicleId);
        }
          async function saveTruckExit() {
            try {
                // Get current weight first
                const weightResponse = await fetch(apiBaseUrl + '/api/scale/GetCurrentWeight');
                if (!weightResponse.ok) {
                    showClientAlert('Tidak dapat membaca berat timbangan!', 'danger');
                    return;
                }
                
                const weightData = await weightResponse.json();
                if (!weightData.IsStable) {
                    showClientAlert('Berat timbangan belum stabil! Tunggu hingga stabil.', 'warning');
                    return;
                }
                
                const vehicleDropdown = document.getElementById('<%= VehicleDropdownExit.ClientID %>');
                const vehicleId = vehicleDropdown.value;
                const remarksExit = document.getElementById('<%= RemarksExit.ClientID %>').value.trim();
                
                if (!vehicleId || vehicleId === '') {
                    showClientAlert('Kendaraan wajib dipilih!', 'danger');
                    return;
                }
                
                // Prepare data
                const exitData = {
                    vehicleId: parseInt(vehicleId),
                    remarks: remarksExit,
                    tareWeight: weightData.Weight
                };
                
                // Call server-side method
                __doPostBack('<%= Page.ClientID %>', 'SAVE_TRUCK_EXIT:' + JSON.stringify(exitData));
                
            } catch (error) {
                showClientAlert('Error: ' + error.message, 'danger');
            }
        }
        
        function refreshHistory() {
            __doPostBack('<%= Page.ClientID %>', 'REFRESH_HISTORY');
        }
        
        function exportToExcel() {
            __doPostBack('<%= Page.ClientID %>', 'EXPORT_EXCEL');
        }
        
        function updateCurrentWeightDisplays() {
            // Update weight displays in truck forms
            const currentWeight = document.getElementById('<%= WeightLabel.ClientID %>').textContent;
            document.getElementById('currentWeightEntry').innerHTML = 'Berat saat ini: <strong>' + currentWeight + ' kg</strong>';
            document.getElementById('currentWeightExit').innerHTML = 'Berat saat ini: <strong>' + currentWeight + ' kg</strong>';
        }
        
        // Auto-refresh every interval
        setInterval(function() {
            if (document.getElementById('<%= AutoRefreshCheckBox.ClientID %>').checked) {
                performAutoRefresh();
            }
        }, refreshInterval);
          // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadClientSettings();
            updateSessionInfo();
            var plateInput = document.getElementById('newVehiclePlate');
            if (plateInput) {
                plateInput.addEventListener('input', function() {
                    this.value = this.value.toUpperCase().replace(/\s+/g, '');
                });
            }
        });
        
        // Vehicle management functions
        function showAddVehicleModal() {
            $('#addVehicleModal').modal('show');
        }
        
        function saveNewVehicle() {
            const plate = document.getElementById('newVehiclePlate').value.trim().toUpperCase();
            const name = document.getElementById('newVehicleName').value.trim();
            const type = document.getElementById('newVehicleType').value.trim();
            const driverName = document.getElementById('newDriverName').value.trim();
            const driverPhone = document.getElementById('newDriverPhone').value.trim();
            
            if (!plate) {
                showClientAlert('No. Polisi harus diisi!', 'warning');
                return;
            }
            
            const vehicleData = {
                noPolisi: plate,
                namaKendaraan: name,
                jenis: type,
                namaSopir: driverName,
                noHp: driverPhone
            };
            
            // Call server-side method to save new vehicle
            __doPostBack('<%= Page.ClientID %>', 'SAVE_NEW_VEHICLE:' + JSON.stringify(vehicleData));
        }
        
        function onVehicleSelectionChanged(dropdown, targetDriverField) {
            const selectedOption = dropdown.options[dropdown.selectedIndex];
            if (selectedOption && selectedOption.dataset) {
                document.getElementById(targetDriverField).value = selectedOption.dataset.driverName || '';
            }
        }
        
        // Setup vehicle dropdown change handlers
        document.addEventListener('DOMContentLoaded', function() {
            const entryDropdown = document.getElementById('<%= VehicleDropdownEntry.ClientID %>');
            const exitDropdown = document.getElementById('<%= VehicleDropdownExit.ClientID %>');
            
            if (entryDropdown) {
                entryDropdown.addEventListener('change', function() {
                    onVehicleSelectionChanged(this, '<%= DriverNameEntry.ClientID %>');
                });
            }
            
            if (exitDropdown) {
                exitDropdown.addEventListener('change', function() {
                    onVehicleSelectionChanged(this, 'exitDriverName');
                });
            }
        });
    </script>
</asp:Content>

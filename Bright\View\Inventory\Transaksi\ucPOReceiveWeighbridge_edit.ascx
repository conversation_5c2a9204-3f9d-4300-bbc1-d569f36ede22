﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucPOReceiveWeighbridge_edit.ascx.vb" Inherits="Bright.ucPOReceiveWeighbridge_edit" %>
<%@ Register assembly="DevExpress.Web.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<style type="text/css">
    .weighbridge-control-panel {
        border: 2px solid #4CAF50;
        border-radius: 12px;
        background: linear-gradient(145deg, #f8fffe 0%, #e8f5e8 100%);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        padding: 20px;
        margin: 15px 0;
    }

    .panel-header {
        background: #2ecc71;
        color: white;
        padding: 10px 15px;
        border-radius: 8px 8px 0 0;
        margin: -20px -20px 15px -20px;
        font-weight: 600;
        font-size: 16px;
    }

    .weight-display {
        background: linear-gradient(145deg, #2c3e50, #34495e);
        color: #ecf0f1;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
        margin: 10px 0;
    }

    .weight-value {
        font-size: 32px;
        font-weight: bold;
        font-family: 'Courier New', monospace;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .weight-status {
        font-size: 14px;
        margin-top: 5px;
        opacity: 0.8;
    }

    .weighbridge-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        margin: 15px 0;
    }

    .status-indicator {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
        margin: 5px;
    }

    .status-connected {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .form-section {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 15px 0;
    }

    .section-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 15px 20px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        color: #495057;
        font-size: 16px;
    }

    .section-body {
        padding: 20px;
    }

    .info-row {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .info-col {
        flex: 1;
        min-width: 200px;
    }

    .readonly-field {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
    }
</style>

<script type="text/javascript">
    var weighbridgeTimer;
    var currentCabangId = 0;
    var isMonitoring = false;
    var currentWeighingStep = 'idle'; // 'idle', 'weigh_in', 'weigh_out'
    var tempWeightData = null;

    // Initialize weighbridge for cabang
    function initializeWeighbridgeForCabang(cabangId) {
        currentCabangId = cabangId;
        updateWeighbridgeStatus('Checking weighbridge connection...', 'warning');

        $.ajax({
            url: '/api/weighbridge/status/' + cabangId,
            method: 'GET',
            success: function(data) {
                if (data && data.isConnected) {
                    updateWeighbridgeStatus('Connected: ' + data.scaleName + ' (' + data.connectionType + ')', 'connected');
                } else {
                    updateWeighbridgeStatus('Not connected: ' + (data.lastError || 'Unknown error'), 'warning');
                }
            },
            error: function(xhr, status, error) {
                updateWeighbridgeStatus('Connection failed: ' + error, 'error');
            }
        });
    }

    // Update weighbridge status
    function updateWeighbridgeStatus(message, type) {
        var statusDiv = document.getElementById('weighbridge-status');
        if (statusDiv) {
            statusDiv.textContent = message;
            statusDiv.className = 'status-indicator status-' + type;
        }

        var timeDiv = document.getElementById('last-update');
        if (timeDiv) {
            timeDiv.textContent = 'Last update: ' + new Date().toLocaleTimeString();
        }
    }

    // Start weight monitoring
    function startMonitoring() {
        if (isMonitoring) return;

        isMonitoring = true;
        updateMonitorButton();

        weighbridgeTimer = setInterval(function() {
            readCurrentWeight();
        }, 2000);

        updateWeighbridgeStatus('Monitoring started...', 'connected');
    }

    // Stop weight monitoring
    function stopMonitoring() {
        if (!isMonitoring) return;

        isMonitoring = false;
        updateMonitorButton();

        if (weighbridgeTimer) {
            clearInterval(weighbridgeTimer);
            weighbridgeTimer = null;
        }

        updateWeighbridgeStatus('Monitoring stopped', 'warning');
    }

    // Update monitor button
    function updateMonitorButton() {
        var btn = ASPxClientControl.GetControlCollection().GetByName('btn_monitor');
        if (btn) {
            btn.SetText(isMonitoring ? 'Stop Monitor' : 'Start Monitor');
            btn.SetImageIconID(isMonitoring ? 'scheduling_timelinescalemonth_16x16' : 'scheduling_timelinescaleday_16x16');
        }
    }

    // Read current weight
    function readCurrentWeight() {
        if (currentCabangId <= 0) return;

        $.ajax({
            url: '/api/weighbridge/weight/' + currentCabangId,
            method: 'GET',
            success: function(data) {
                if (data) {
                    updateWeightDisplay(data);
                    tempWeightData = data;

                    if (data.isStable && currentWeighingStep !== 'idle') {
                        // Auto-save stable weight during weighing process
                        saveWeight(data);
                    } else if (data.isStable) {
                        updateWeighbridgeStatus('Weight stable: ' + data.weight + ' ' + data.unit, 'connected');
                    } else {
                        updateWeighbridgeStatus('Weight unstable: ' + data.weight + ' ' + data.unit, 'warning');
                    }
                }
            },
            error: function() {
                updateWeighbridgeStatus('Failed to read weight', 'error');
            }
        });
    }

    // Update weight display
    function updateWeightDisplay(data) {
        var valueEl = document.getElementById('current-weight-value');
        var statusEl = document.getElementById('weight-status');

        if (valueEl) {
            valueEl.textContent = data.weight + ' ' + data.unit;
        }

        if (statusEl) {
            statusEl.textContent = data.isStable ? 'Stable' : 'Not Stable';
            statusEl.style.color = data.isStable ? '#2ecc71' : '#f39c12';
        }
    }

    // Save weight to server
    function saveWeight(data) {
        var weightData = {
            step: currentWeighingStep,
            weight: data.weight,
            unit: data.unit,
            timestamp: data.timestamp,
            scaleId: data.scaleId
        };

        cp_poreceiveweighbridge.PerformCallback('save_weight;' + JSON.stringify(weightData));

        // Stop monitoring after save
        stopMonitoring();
        currentWeighingStep = 'idle';

        updateWeighbridgeStatus('Weight saved: ' + data.weight + ' ' + data.unit, 'connected');
    }

    // Start weigh-in process
    function startWeighIn() {
        if (currentCabangId <= 0) {
            alert('Please select a branch first!');
            return;
        }

        // Validate required fields
        var kendaraanValue = cb_kendaraan.GetValue();
        var driverName = txt_driver_name.GetText();

        if (!kendaraanValue || kendaraanValue <= 0) {
            alert('Please select a vehicle first!');
            return;
        }

        if (!driverName || driverName.trim() === '') {
            alert('Please enter driver name!');
            return;
        }

        currentWeighingStep = 'weigh_in';
        startMonitoring();
        updateWeighbridgeStatus('Starting weigh-in process...', 'warning');
    }

    // Start weigh-out process
    function startWeighOut() {
        // Check if weigh-in is completed
        var grossWeight = 0;
        try {
            if (txt_gross_weight && txt_gross_weight.GetValue) {
                grossWeight = parseFloat(txt_gross_weight.GetValue()) || 0;
            }
        } catch (ex) {
            console.log('Error getting gross weight: ' + ex.message);
        }

        if (grossWeight <= 0) {
            alert('Weigh-in must be completed first!');
            return;
        }

        currentWeighingStep = 'weigh_out';
        startMonitoring();
        updateWeighbridgeStatus('Starting weigh-out process...', 'warning');
    }

    // Manual weight reading
    function readManualWeight() {
        readCurrentWeight();
    }

    // Test function
    function testWeight() {
        var testData = {
            weight: Math.round((Math.random() * 1000 + 500) * 100) / 100,
            unit: 'kg',
            isStable: true,
            timestamp: new Date().toISOString(),
            scaleId: 'TEST-001'
        };

        updateWeightDisplay(testData);
        tempWeightData = testData;
        updateWeighbridgeStatus('Test weight: ' + testData.weight + ' kg', 'connected');
    }

    // Get current cabang ID
    function getCurrentCabangId() {
        try {
            if (cb_cabang && cb_cabang.GetValue) {
                return parseInt(cb_cabang.GetValue()) || 0;
            }
            return 0;
        } catch (ex) {
            return 0;
        }
    }

    // Cabang changed event
    function onCabangChanged() {
        var cabangId = getCurrentCabangId();
        if (cabangId > 0) {
            currentCabangId = cabangId;
            initializeWeighbridgeForCabang(cabangId);
        }
    }

    // Post transaction
    function postTransaction() {
        var receiveId = getCurrentReceiveId();
        if (receiveId <= 0) {
            alert('Please save the receive first!');
            return;
        }

        if (confirm('Post this transaction to inventory?')) {
            cp_poreceiveweighbridge.PerformCallback('post_transaction;' + receiveId);
        }
    }

    // Get current receive ID
    function getCurrentReceiveId() {
        try {
            if (hf_receive_id && hf_receive_id.value) {
                return parseInt(hf_receive_id.value) || 0;
            }
            return 0;
        } catch (ex) {
            return 0;
        }
    }

    // Create from PO
    function createFromPO() {
        var poId = cb_po.GetValue();
        var cabangId = cb_cabang.GetValue();
        var lokasiId = cb_lokasi.GetValue();

        if (!poId || poId <= 0) {
            alert('Please select a PO first!');
            return;
        }

        if (!cabangId || cabangId <= 0) {
            alert('Please select a branch first!');
            return;
        }

        if (!lokasiId || lokasiId <= 0) {
            alert('Please select a location first!');
            return;
        }

        if (confirm('Create receive from selected PO?')) {
            cp_poreceiveweighbridge.PerformCallback('create_from_po;' + poId + ';' + cabangId + ';' + lokasiId);
        }
    }

    // Update weighbridge control state based on status
    function updateWeighbridgeControlState(status) {
        try {
            var weighInBtn = document.getElementById('btn-weigh-in');
            var weighOutBtn = document.getElementById('btn-weigh-out');
            var monitorBtn = document.getElementById('btn-monitor');
            var postBtn = document.getElementById('btn-post');

            // Reset all buttons
            if (weighInBtn) weighInBtn.disabled = false;
            if (weighOutBtn) weighOutBtn.disabled = true;
            if (monitorBtn) monitorBtn.disabled = false;
            if (postBtn) postBtn.disabled = true;

            switch (status.toUpperCase()) {
                case 'DRAFT':
                    // Allow weigh-in, disable others
                    if (weighOutBtn) weighOutBtn.disabled = true;
                    if (postBtn) postBtn.disabled = true;
                    break;

                case 'WEIGH_IN':
                    // Allow weigh-out, disable weigh-in
                    if (weighInBtn) weighInBtn.disabled = true;
                    if (weighOutBtn) weighOutBtn.disabled = false;
                    if (postBtn) postBtn.disabled = true;
                    break;

                case 'COMPLETED':
                    // Allow posting, disable weighing
                    if (weighInBtn) weighInBtn.disabled = true;
                    if (weighOutBtn) weighOutBtn.disabled = true;
                    if (postBtn) postBtn.disabled = false;
                    break;

                case 'POSTED':
                    // Disable all
                    if (weighInBtn) weighInBtn.disabled = true;
                    if (weighOutBtn) weighOutBtn.disabled = true;
                    if (monitorBtn) monitorBtn.disabled = true;
                    if (postBtn) postBtn.disabled = true;
                    break;
            }
        } catch (ex) {
            console.log('Error updating weighbridge control state: ' + ex.message);
        }
    }

    // Initialize on page load
    $(document).ready(function() {
        // Initialize weighbridge status
        var cabangId = getCurrentCabangId();
        if (cabangId > 0) {
            initializeWeighbridgeForCabang(cabangId);
        }
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        stopMonitoring();
    });
</script>

<asp:HiddenField ID="hf_receive_id" runat="server" ClientIDMode="Static" />

<dx:ASPxFormLayout ID="ASPxFormLayout1" runat="server" Width="100%" AlignItemCaptionsInAllGroups="True">
    <SettingsAdaptivity AdaptivityMode="SingleColumnWindowLimit" />

    <Items>
        <dx:LayoutGroup Caption="Basic Information" ColCount="3" ShowCaption="True">
            <Items>
                <dx:LayoutItem Caption="No. Receive" FieldName="NoReceive">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_no_receive" runat="server" Width="100%" ReadOnly="true" CssClass="readonly-field">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Date" FieldName="Tanggal">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxDateEdit ID="dt_tanggal" runat="server" Width="100%">
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Status" FieldName="Status">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_status" runat="server" Width="100%" ReadOnly="true" CssClass="readonly-field">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Area" FieldName="Area_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxComboBox ID="cb_area" runat="server" Width="100%"
                                ValueField="Id" TextFormatString="{0} - {1}">
                                <Columns>
                                    <dx:ListBoxColumn FieldName="KodeArea" />
                                    <dx:ListBoxColumn FieldName="NamaArea" />
                                </Columns>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Branch" FieldName="Cabang_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxComboBox ID="cb_cabang" runat="server" Width="100%"
                                ValueField="Id" TextFormatString="{0} - {1}" ClientInstanceName="cb_cabang">
                                <ClientSideEvents SelectedIndexChanged="function(s, e) { onCabangChanged(); }" />
                                <Columns>
                                    <dx:ListBoxColumn FieldName="KodeCabang" />
                                    <dx:ListBoxColumn FieldName="NamaCabang" />
                                </Columns>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Location" FieldName="Lokasi_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxComboBox ID="cb_lokasi" runat="server" Width="100%"
                                ValueField="Id" TextFormatString="{0} - {1}" ClientInstanceName="cb_lokasi">
                                <Columns>
                                    <dx:ListBoxColumn FieldName="KodeLokasi" />
                                    <dx:ListBoxColumn FieldName="NamaLokasi" />
                                </Columns>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Purchase Order" FieldName="PO_id" ColSpan="2">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <dx:ASPxComboBox ID="cb_po" runat="server" Width="85%"
                                    ValueField="Id" TextFormatString="{0}" ClientInstanceName="cb_po">
                                    <Columns>
                                        <dx:ListBoxColumn FieldName="NoPO" />
                                    </Columns>
                                </dx:ASPxComboBox>
                                <dx:ASPxButton ID="btn_create_from_po" runat="server" Text="Create from PO"
                                    Theme="MetropolisBlue" Width="15%" AutoPostBack="false" CausesValidation="false">
                                    <ClientSideEvents Click="function(s, e) { createFromPO(); }" />
                                    <Image IconID="actions_addfile_16x16" />
                                </dx:ASPxButton>
                            </div>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Description" FieldName="Keterangan" ColSpan="3">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxMemo ID="txt_keterangan" runat="server" Width="100%" Height="60px">
                            </dx:ASPxMemo>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>

        <dx:LayoutGroup Caption="Vehicle Information" ColCount="3" ShowCaption="True">
            <Items>
                <dx:LayoutItem Caption="Vehicle" FieldName="Kendaraan_id">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <div style="display: flex; gap: 5px; align-items: center;">
                                <dx:ASPxComboBox ID="cb_kendaraan" runat="server" Width="85%"
                                    ValueField="Id" TextFormatString="{0} - {1}" ClientInstanceName="cb_kendaraan">
                                    <Columns>
                                        <dx:ListBoxColumn FieldName="NoPolisi" Caption="License Plate" Width="120px" />
                                        <dx:ListBoxColumn FieldName="NamaKendaraan" Caption="Vehicle Name" Width="150px" />
                                    </Columns>
                                </dx:ASPxComboBox>
                                <dx:ASPxButton ID="btn_add_vehicle" runat="server" Text="+"
                                    Width="15%" Theme="MetropolisBlue" ToolTip="Add New Vehicle">
                                    <Image IconID="actions_add_16x16" />
                                </dx:ASPxButton>
                            </div>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Driver Name" FieldName="DriverName">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_driver_name" runat="server" Width="100%" ClientInstanceName="txt_driver_name">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Driver Phone" FieldName="DriverPhone">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_driver_phone" runat="server" Width="100%">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>

        <dx:LayoutItem ShowCaption="False" ColSpan="1">
            <LayoutItemNestedControlCollection>
                <dx:LayoutItemNestedControlContainer>
                    <div class="weighbridge-control-panel">
                        <div class="panel-header">
                            <i class="fa fa-balance-scale"></i> Weighbridge Control Panel
                        </div>

                        <div class="weight-display">
                            <div class="weight-value" id="current-weight-value">-- kg</div>
                            <div class="weight-status" id="weight-status">(Idle)</div>
                        </div>

                        <div class="weighbridge-buttons">
                            <dx:ASPxButton ID="btn_weigh_in" runat="server" Text="Weigh In"
                                Theme="MetropolisBlue" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { startWeighIn(); }" />
                                <Image IconID="actions_additem_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_weigh_out" runat="server" Text="Weigh Out"
                                Theme="MetropolisBlue" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { startWeighOut(); }" />
                                <Image IconID="actions_removeitem_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_read_manual" runat="server" Text="Read Manual"
                                Theme="Office365" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { readManualWeight(); }" />
                                <Image IconID="actions_refresh_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_monitor" runat="server" Text="Start Monitor"
                                Theme="Office365" AutoPostBack="false" CausesValidation="false" ClientInstanceName="btn_monitor">
                                <ClientSideEvents Click="function(s, e) {
                                    if (isMonitoring) stopMonitoring(); else startMonitoring();
                                }" />
                                <Image IconID="scheduling_timelinescaleday_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_test_weight" runat="server" Text="Test Weight"
                                Theme="Office365" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { testWeight(); }" />
                                <Image IconID="actions_show_16x16" />
                            </dx:ASPxButton>
                        </div>

                        <div style="margin-top: 15px;">
                            <div id="weighbridge-status" class="status-indicator status-warning">
                                Initializing weighbridge...
                            </div>
                            <div id="last-update" style="font-size: 12px; color: #666; margin-top: 10px;">
                                Last update: --
                            </div>
                        </div>
                    </div>
                </dx:LayoutItemNestedControlContainer>
            </LayoutItemNestedControlCollection>
        </dx:LayoutItem>

        <dx:LayoutGroup Caption="Weight Information" ColCount="4" ShowCaption="True">
            <Items>
                <dx:LayoutItem Caption="Gross Weight (kg)" FieldName="GrossWeight">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxSpinEdit ID="txt_gross_weight" runat="server" Width="100%"
                                NumberType="Float" DecimalPlaces="3" ClientInstanceName="txt_gross_weight" ReadOnly="true">
                            </dx:ASPxSpinEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Tare Weight (kg)" FieldName="TareWeight">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxSpinEdit ID="txt_tare_weight" runat="server" Width="100%"
                                NumberType="Float" DecimalPlaces="3" ReadOnly="true">
                            </dx:ASPxSpinEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Net Weight (kg)" FieldName="NetWeight">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxSpinEdit ID="txt_net_weight" runat="server" Width="100%"
                                NumberType="Float" DecimalPlaces="3" ReadOnly="true" CssClass="readonly-field">
                            </dx:ASPxSpinEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Weight Difference" ShowCaption="True">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_weight_diff" runat="server" Width="100%"
                                ReadOnly="true" CssClass="readonly-field">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Weigh In Time" FieldName="WeighInTime">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxDateEdit ID="dt_weigh_in_time" runat="server" Width="100%"
                                EditFormat="DateTime" DisplayFormatString="dd/MM/yyyy HH:mm:ss" ReadOnly="true">
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Weigh Out Time" FieldName="WeighOutTime">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxDateEdit ID="dt_weigh_out_time" runat="server" Width="100%"
                                EditFormat="DateTime" DisplayFormatString="dd/MM/yyyy HH:mm:ss" ReadOnly="true">
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Weighing Duration" ShowCaption="True">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_duration" runat="server" Width="100%"
                                ReadOnly="true" CssClass="readonly-field">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Quality Status" ShowCaption="True">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxComboBox ID="cb_quality_status" runat="server" Width="100%">
                                <Items>
                                    <dx:ListEditItem Text="PENDING" Value="PENDING" />
                                    <dx:ListEditItem Text="PASSED" Value="PASSED" />
                                    <dx:ListEditItem Text="FAILED" Value="FAILED" />
                                    <dx:ListEditItem Text="CONDITIONAL" Value="CONDITIONAL" />
                                </Items>
                            </dx:ASPxComboBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>

        <dx:LayoutGroup Caption="Posting Information" ColCount="3" ShowCaption="True">
            <Items>
                <dx:LayoutItem Caption="Posted" FieldName="Posted">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxCheckBox ID="chk_posted" runat="server" ReadOnly="true">
                            </dx:ASPxCheckBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Posted By" FieldName="PostedBy">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxTextBox ID="txt_posted_by" runat="server" Width="100%"
                                ReadOnly="true" CssClass="readonly-field">
                            </dx:ASPxTextBox>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>

                <dx:LayoutItem Caption="Posted Date" FieldName="PostedDate">
                    <LayoutItemNestedControlCollection>
                        <dx:LayoutItemNestedControlContainer>
                            <dx:ASPxDateEdit ID="dt_posted_date" runat="server" Width="100%"
                                EditFormat="DateTime" DisplayFormatString="dd/MM/yyyy HH:mm:ss" ReadOnly="true">
                            </dx:ASPxDateEdit>
                        </dx:LayoutItemNestedControlContainer>
                    </LayoutItemNestedControlCollection>
                </dx:LayoutItem>
            </Items>
        </dx:LayoutGroup>

        <dx:LayoutItem ShowCaption="False" ColSpan="1">
            <LayoutItemNestedControlCollection>
                <dx:LayoutItemNestedControlContainer>
                    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 15px 0;">
                        <div style="background: #007bff; color: white; padding: 10px 15px; border-radius: 8px 8px 0 0; margin: -20px -20px 15px -20px; font-weight: 600; font-size: 16px;">
                            <i class="fa fa-cogs"></i> Action Controls
                        </div>

                        <div style="display: flex; gap: 10px; flex-wrap: wrap; align-items: center; justify-content: center;">
                            <dx:ASPxButton ID="btn_save" runat="server" Text="Save"
                                Theme="MetropolisBlue" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { cp_poreceiveweighbridge.PerformCallback('save'); }" />
                                <Image IconID="actions_save_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_post_transaction" runat="server" Text="Post Transaction"
                                Theme="MetropolisBlue" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { postTransaction(); }" />
                                <Image IconID="actions_apply_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_reset" runat="server" Text="Reset"
                                Theme="Office365" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { cp_poreceiveweighbridge.PerformCallback('reset'); }" />
                                <Image IconID="actions_reset_16x16" />
                            </dx:ASPxButton>

                            <dx:ASPxButton ID="btn_print" runat="server" Text="Print"
                                Theme="Office365" AutoPostBack="false" CausesValidation="false">
                                <ClientSideEvents Click="function(s, e) { var receiveId = getCurrentReceiveId(); if (receiveId > 0) cp_poreceiveweighbridge.PerformCallback('btn_print1;' + receiveId); else alert('Please save first!'); }" />
                                <Image IconID="reports_report_16x16" />
                            </dx:ASPxButton>
                        </div>

                        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e9ecef;">
                            <div style="font-size: 12px; color: #6c757d;">
                                <strong>Status Flow:</strong> DRAFT → WEIGH_IN → COMPLETED → POSTED
                            </div>
                        </div>
                    </div>
                </dx:LayoutItemNestedControlContainer>
            </LayoutItemNestedControlCollection>
        </dx:LayoutItem>
    </Items>
</dx:ASPxFormLayout>

<!-- Detail Items Grid Section -->
<div class="form-section" style="margin-top: 20px;">
    <div class="section-header">
        <i class="fa fa-list"></i> Receive Detail Items
    </div>
    <div class="section-body">
        <dx:ASPxGridView ID="grd_receive_lines" runat="server"
            AutoGenerateColumns="False"
            KeyFieldName="Id"
            Width="100%"
            ClientInstanceName="grd_receive_lines">

            <SettingsPager PageSize="10">
                <PageSizeItemSettings Visible="True" Items="5,10,20" />
            </SettingsPager>

            <Settings ShowFooter="True" />
            <SettingsBehavior AllowFocusedRow="True" />

            <Columns>
                <dx:GridViewCommandColumn VisibleIndex="0" ShowClearFilterButton="False" Width="80px">
                    <CustomButtons>
                        <dx:GridViewCommandColumnCustomButton ID="btn_edit_line" Text="Edit">
                            <Image IconID="actions_edit_16x16" />
                        </dx:GridViewCommandColumnCustomButton>
                        <dx:GridViewCommandColumnCustomButton ID="btn_delete_line" Text="Delete">
                            <Image IconID="iconbuilder_actions_delete_svg_16x16" />
                        </dx:GridViewCommandColumnCustomButton>
                    </CustomButtons>
                </dx:GridViewCommandColumn>

                <dx:GridViewDataTextColumn FieldName="tr_po_line.tm_item_master.KodeItem" VisibleIndex="1" Caption="Item Code" Width="120px">
                    <HeaderStyle HorizontalAlign="Center" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataTextColumn FieldName="tr_po_line.tm_item_master.NamaItem" VisibleIndex="2" Caption="Item Name" Width="200px">
                    <HeaderStyle HorizontalAlign="Center" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataTextColumn FieldName="tr_po_line.QtyOrdered" VisibleIndex="3" Caption="Qty Ordered" Width="100px">
                    <PropertiesTextEdit DisplayFormatString="{0:N2}" />
                    <HeaderStyle HorizontalAlign="Center" />
                    <CellStyle HorizontalAlign="Right" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataTextColumn FieldName="QtyReceived" VisibleIndex="4" Caption="Qty Received" Width="100px">
                    <PropertiesTextEdit DisplayFormatString="{0:N2}" />
                    <HeaderStyle HorizontalAlign="Center" />
                    <CellStyle HorizontalAlign="Right" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataTextColumn FieldName="tr_po_line.tm_satuan.NamaSatuan" VisibleIndex="5" Caption="Unit" Width="80px">
                    <HeaderStyle HorizontalAlign="Center" />
                    <CellStyle HorizontalAlign="Center" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataTextColumn FieldName="QualityStatus" VisibleIndex="6" Caption="Quality" Width="100px">
                    <HeaderStyle HorizontalAlign="Center" />
                    <CellStyle HorizontalAlign="Center" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataTextColumn FieldName="BatchNumber" VisibleIndex="7" Caption="Batch" Width="120px">
                    <HeaderStyle HorizontalAlign="Center" />
                </dx:GridViewDataTextColumn>

                <dx:GridViewDataDateColumn FieldName="ExpiredDate" VisibleIndex="8" Caption="Expired Date" Width="100px">
                    <PropertiesDateEdit DisplayFormatString="dd MMM yyyy" />
                    <HeaderStyle HorizontalAlign="Center" />
                    <CellStyle HorizontalAlign="Center" />
                </dx:GridViewDataDateColumn>
            </Columns>

            <TotalSummary>
                <dx:ASPxSummaryItem FieldName="QtyReceived" SummaryType="Sum" DisplayFormat="Total Qty: {0:N2}" />
            </TotalSummary>

            <Toolbars>
                <dx:GridViewToolbar>
                    <Items>
                        <dx:GridViewToolbarItem Name="btn_add_line" Text="Add Item">
                            <Image IconID="actions_addfile_16x16" />
                        </dx:GridViewToolbarItem>
                        <dx:GridViewToolbarItem Name="btn_refresh_lines" Text="Refresh">
                            <Image IconID="actions_refresh_16x16" />
                        </dx:GridViewToolbarItem>
                    </Items>
                </dx:GridViewToolbar>
            </Toolbars>
        </dx:ASPxGridView>
    </div>
</div>

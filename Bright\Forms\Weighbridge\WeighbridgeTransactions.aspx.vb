Imports System.Data
Imports System.Data.SqlClient
Imports System.Web.UI.WebControls
Imports System.IO
Imports System.Text

Public Class WeighbridgeTransactions
    Inherits System.Web.UI.Page

    ' Properties for pagination
    Private ReadOnly pageSize As Integer = 20
    Private currentPage As Integer = 1

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not Page.IsPostBack Then
            InitializePage()
            LoadTransactions()
            LoadSummaryData()
        End If
    End Sub

    Private Sub InitializePage()
        Try
            ' Set default date filters to today
            FromDateTextBox.Text = DateTime.Today.ToString("yyyy-MM-dd")
            ToDateTextBox.Text = DateTime.Today.ToString("yyyy-MM-dd")
            
            ' Get current page from query string
            If Request.QueryString("page") IsNot Nothing Then
                Integer.TryParse(Request.QueryString("page"), currentPage)
                If currentPage < 1 Then currentPage = 1
            End If
        Catch ex As Exception
            ShowMessage("Error initializing page: " & ex.Message)
        End Try
    End Sub

    Private Sub LoadSummaryData()
        Try
            ' For demo purposes, we'll create sample summary data
            ' In production, this would query your database
            
            Dim totalTransactions = GetTotalTransactions()
            Dim todayTransactions = GetTodayTransactions()
            Dim totalWeight = GetTotalWeight()
            Dim averageWeight = GetAverageWeight()
            
            TotalTransactionsLabel.Text = totalTransactions.ToString()
            TodayTransactionsLabel.Text = todayTransactions.ToString()
            TotalWeightLabel.Text = totalWeight.ToString("F2")
            AverageWeightLabel.Text = averageWeight.ToString("F2")
            
        Catch ex As Exception
            ShowMessage("Error loading summary data: " & ex.Message)
        End Try
    End Sub

    Private Sub LoadTransactions()
        Try
            ' Clear existing transactions
            TransactionsPanel.Controls.Clear()
            
            ' Get filtered transactions
            Dim transactions = GetFilteredTransactions()
            
            If transactions.Count = 0 Then
                EmptyStatePanel.Visible = True
                Return
            Else
                EmptyStatePanel.Visible = False
            End If
            
            ' Display transactions
            For Each transaction In transactions
                Dim transactionCard = CreateTransactionCard(transaction)
                TransactionsPanel.Controls.Add(transactionCard)
            Next
            
            ' Update pagination
            UpdatePagination(transactions.Count)
            
        Catch ex As Exception
            ShowMessage("Error loading transactions: " & ex.Message)
        End Try
    End Sub

    Private Function GetFilteredTransactions() As List(Of WeighbridgeTransaction)
        ' For demo purposes, create sample data
        ' In production, this would query your database with filters
        
        Dim transactions As New List(Of WeighbridgeTransaction)
        
        ' Sample data generation
        Dim random As New Random()
        Dim materials() As String = {"Coal", "Sand", "Gravel", "Steel", "Cement"}
        Dim statuses() As String = {"Completed", "Pending", "Cancelled"}
        Dim vehicleTypes() As String = {"Truck", "Trailer", "Container"}
        
        For i As Integer = 1 To 50
            Dim transaction As New WeighbridgeTransaction With {
                .TransactionId = "TXN" & DateTime.Now.ToString("yyyyMMdd") & i.ToString("000"),
                .VehicleNumber = "B" & random.Next(1000, 9999) & "ABC",
                .DriverName = "Driver " & i,
                .MaterialType = materials(random.Next(materials.Length)),
                .GrossWeight = random.Next(15000, 45000),
                .TareWeight = random.Next(8000, 15000),
                .WeighInTime = DateTime.Now.AddHours(-random.Next(1, 72)),
                .WeighOutTime = DateTime.Now.AddHours(-random.Next(0, 48)), .Status = statuses(random.Next(statuses.Length)),
                .OperatorName = "Operator " & random.Next(1, 5),
                .Notes = If(random.Next(1, 4) = 1, "Special handling required", ""),
                .VehicleType = vehicleTypes(random.Next(vehicleTypes.Length))
            }
            transaction.NetWeight = transaction.GrossWeight - transaction.TareWeight
            transactions.Add(transaction)
        Next

        ' Apply filters
        transactions = ApplyFilters(transactions)

        ' Apply pagination
        Dim startIndex = (currentPage - 1) * pageSize
        Return transactions.Skip(startIndex).Take(pageSize).ToList()
    End Function

    Private Function ApplyFilters(transactions As List(Of WeighbridgeTransaction)) As List(Of WeighbridgeTransaction)
        Dim filtered = transactions.AsEnumerable()

        ' Date filter
        If Not String.IsNullOrEmpty(FromDateTextBox.Text) Then
            Dim fromDate As DateTime
            If DateTime.TryParse(FromDateTextBox.Text, fromDate) Then
                filtered = filtered.Where(Function(t) t.WeighInTime.Date >= fromDate.Date)
            End If
        End If

        If Not String.IsNullOrEmpty(ToDateTextBox.Text) Then
            Dim toDate As DateTime
            If DateTime.TryParse(ToDateTextBox.Text, toDate) Then
                filtered = filtered.Where(Function(t) t.WeighInTime.Date <= toDate.Date)
            End If
        End If

        ' Vehicle number filter
        If Not String.IsNullOrEmpty(VehicleNumberTextBox.Text) Then
            Dim vehicleFilter = VehicleNumberTextBox.Text.Trim().ToLower()
            filtered = filtered.Where(Function(t) t.VehicleNumber.ToLower().Contains(vehicleFilter))
        End If

        ' Driver name filter
        If Not String.IsNullOrEmpty(DriverNameTextBox.Text) Then
            Dim driverFilter = DriverNameTextBox.Text.Trim().ToLower()
            filtered = filtered.Where(Function(t) t.DriverName.ToLower().Contains(driverFilter))
        End If

        ' Status filter
        If Not String.IsNullOrEmpty(StatusDropDown.SelectedValue) Then
            filtered = filtered.Where(Function(t) t.Status = StatusDropDown.SelectedValue)
        End If

        ' Material type filter
        If Not String.IsNullOrEmpty(MaterialTypeDropDown.SelectedValue) Then
            filtered = filtered.Where(Function(t) t.MaterialType = MaterialTypeDropDown.SelectedValue)
        End If

        ' Weight range filter
        If Not String.IsNullOrEmpty(MinWeightTextBox.Text) Then
            Dim minWeight As Decimal
            If Decimal.TryParse(MinWeightTextBox.Text, minWeight) Then
                filtered = filtered.Where(Function(t) t.NetWeight >= minWeight)
            End If
        End If

        If Not String.IsNullOrEmpty(MaxWeightTextBox.Text) Then
            Dim maxWeight As Decimal
            If Decimal.TryParse(MaxWeightTextBox.Text, maxWeight) Then
                filtered = filtered.Where(Function(t) t.NetWeight <= maxWeight)
            End If
        End If

        Return filtered.ToList()
    End Function

    Private Function CreateTransactionCard(transaction As WeighbridgeTransaction) As Panel
        Dim card As New Panel()
        card.CssClass = "transaction-card"

        Dim html As New StringBuilder()
        html.Append("<div class='transaction-info'>")

        ' Transaction ID
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Transaction ID</div>")
        html.AppendFormat("<div class='info-value'>{0}</div>", transaction.TransactionId)
        html.AppendFormat("</div>")

        ' Vehicle Number
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Vehicle Number</div>")
        html.AppendFormat("<div class='info-value'>{0}</div>", transaction.VehicleNumber)
        html.AppendFormat("</div>")

        ' Driver Name
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Driver</div>")
        html.AppendFormat("<div class='info-value'>{0}</div>", transaction.DriverName)
        html.AppendFormat("</div>")

        ' Material Type
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Material</div>")
        html.AppendFormat("<div class='info-value'>{0}</div>", transaction.MaterialType)
        html.AppendFormat("</div>")

        ' Net Weight
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Net Weight</div>")
        html.AppendFormat("<div class='info-value weight-display'>{0:N2} kg</div>", transaction.NetWeight)
        html.AppendFormat("</div>")

        ' Weigh In Time
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Weigh In</div>")
        html.AppendFormat("<div class='info-value'>{0:dd/MM/yyyy HH:mm}</div>", transaction.WeighInTime)
        html.AppendFormat("</div>")

        ' Status
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Status</div>")
        Dim statusClass = GetStatusClass(transaction.Status)
        html.AppendFormat("<div class='status-badge {0}'>{1}</div>", statusClass, transaction.Status)
        html.AppendFormat("</div>")

        ' Actions
        html.AppendFormat("<div class='info-item'>")
        html.AppendFormat("<div class='info-label'>Actions</div>")
        html.AppendFormat("<div class='info-value'>")
        html.AppendFormat("<a href='#' class='btn btn-primary' style='font-size: 12px; padding: 4px 8px; margin-right: 5px;'>View</a>")
        html.AppendFormat("<a href='#' class='btn btn-success' style='font-size: 12px; padding: 4px 8px;'>Print</a>")
        html.AppendFormat("</div>")
        html.AppendFormat("</div>")

        html.Append("</div>")

        ' Add notes if available
        If Not String.IsNullOrEmpty(transaction.Notes) Then
            html.AppendFormat("<div style='margin-top: 10px; padding-top: 10px; border-top: 1px solid #e9ecef; font-size: 0.9em; color: #6c757d;'>")
            html.AppendFormat("<strong>Notes:</strong> {0}", transaction.Notes)
            html.AppendFormat("</div>")
        End If

        card.Controls.Add(New LiteralControl(html.ToString()))
        Return card
    End Function

    Private Function GetStatusClass(status As String) As String
        Select Case status.ToLower()
            Case "completed"
                Return "status-completed"
            Case "pending"
                Return "status-pending"
            Case "cancelled"
                Return "status-cancelled"
        End Select
        Return "status-pending"
    End Function

    Private Sub UpdatePagination(totalRecords As Integer)
        Dim totalPages = Math.Ceiling(totalRecords / pageSize)

        If totalPages <= 1 Then
            PaginationLabel.Text = ""
            Return
        End If

        Dim pagination As New StringBuilder()
        pagination.Append($"Page {currentPage} of {totalPages} | ")

        ' Previous page
        If currentPage > 1 Then
            pagination.AppendFormat("<a href='?page={0}'>« Previous</a> ", currentPage - 1)
        End If

        ' Page numbers
        Dim startPage = Math.Max(1, currentPage - 2)
        Dim endPage = Math.Min(totalPages, currentPage + 2)

        For i = startPage To endPage
            If i = currentPage Then
                pagination.AppendFormat("<span class='current'>{0}</span> ", i)
            Else
                pagination.AppendFormat("<a href='?page={0}'>{0}</a> ", i)
            End If
        Next

        ' Next page
        If currentPage < totalPages Then
            pagination.AppendFormat("<a href='?page={0}'>Next »</a>", currentPage + 1)
        End If

        PaginationLabel.Text = pagination.ToString()
    End Sub

    ' Event Handlers
    Protected Sub SearchButton_Click(sender As Object, e As EventArgs) Handles SearchButton.Click
        currentPage = 1
        LoadTransactions()
        LoadSummaryData()
    End Sub

    Protected Sub ClearFiltersButton_Click(sender As Object, e As EventArgs) Handles ClearFiltersButton.Click
        FromDateTextBox.Text = DateTime.Today.ToString("yyyy-MM-dd")
        ToDateTextBox.Text = DateTime.Today.ToString("yyyy-MM-dd")
        VehicleNumberTextBox.Text = ""
        DriverNameTextBox.Text = ""
        StatusDropDown.SelectedIndex = 0
        MaterialTypeDropDown.SelectedIndex = 0
        MinWeightTextBox.Text = ""
        MaxWeightTextBox.Text = ""

        currentPage = 1
        LoadTransactions()
        LoadSummaryData()
    End Sub

    Protected Sub ExportButton_Click(sender As Object, e As EventArgs) Handles ExportButton.Click
        Try
            ' Get all transactions (without pagination)
            Dim allTransactions = GetFilteredTransactions()

            ' Create CSV content
            Dim csv As New StringBuilder()
            csv.AppendLine("Transaction ID,Vehicle Number,Driver Name,Material Type,Gross Weight,Tare Weight,Net Weight,Weigh In Time,Weigh Out Time,Status,Operator,Notes")

            For Each transaction In allTransactions
                csv.AppendFormat("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11}" & vbCrLf,
                    transaction.TransactionId,
                    transaction.VehicleNumber,
                    transaction.DriverName,
                    transaction.MaterialType,
                    transaction.GrossWeight,
                    transaction.TareWeight,
                    transaction.NetWeight,
                    transaction.WeighInTime.ToString("yyyy-MM-dd HH:mm:ss"), transaction.WeighOutTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    transaction.Status,
                    transaction.OperatorName,
                    transaction.Notes.Replace(",", ";"))
            Next

            ' Send file to browser
            Response.Clear()
            Response.ContentType = "text/csv"
            Response.AddHeader("Content-Disposition", $"attachment; filename=WeighbridgeTransactions_{DateTime.Now:yyyyMMdd_HHmmss}.csv")
            Response.Write(csv.ToString())
            Response.End()

        Catch ex As Exception
            ShowMessage("Error exporting data: " & ex.Message)
        End Try
    End Sub

    ' Helper methods for summary data (demo data)
    Private Function GetTotalTransactions() As Integer
        Return 1250 ' Demo value
    End Function

    Private Function GetTodayTransactions() As Integer
        Return 45 ' Demo value
    End Function

    Private Function GetTotalWeight() As Decimal
        Return 125750.5 ' Demo value
    End Function

    Private Function GetAverageWeight() As Decimal
        Return 12575.25 ' Demo value
    End Function

    Private Sub ShowMessage(message As String)
        ' Display message to user (implement as needed)
        ClientScript.RegisterStartupScript(Me.GetType(), "alert", $"alert('{message}');", True)
    End Sub

    ' Data class for transactions
    Public Class WeighbridgeTransaction
        Public Property TransactionId As String
        Public Property VehicleNumber As String
        Public Property DriverName As String
        Public Property MaterialType As String
        Public Property GrossWeight As Decimal
        Public Property TareWeight As Decimal
        Public Property NetWeight As Decimal
        Public Property WeighInTime As DateTime
        Public Property WeighOutTime As DateTime
        Public Property Status As String
        Public Property OperatorName As String
        Public Property Notes As String
        Public Property VehicleType As String
    End Class
End Class

# Weighbridge Configuration Implementation Guide

## Overview
Implementasi konfigurasi jembatan timbangan dengan dukungan multiple connection types termasuk mode DUMMY untuk pengembangan.

## Features
- ✅ **Multiple Connection Types**: DUMMY, SERIAL, TCP, USB
- ✅ **DUMMY Mode**: Simulasi untuk pengembangan tanpa hardware
- ✅ **Real-time Testing**: Interface untuk test koneksi dan monitoring
- ✅ **Configuration Management**: CRUD konfigurasi per cabang
- ✅ **API Integration**: RESTful API untuk integrasi dengan aplikasi lain

## Database Structure

### Tables Created
1. **tm_weighbridge_config** - Konfigurasi jembatan timbangan
2. **tr_po_receive_weighbridge** - Header transaksi receive via timbangan
3. **tr_po_receive_weighbridge_line** - Detail transaksi receive
4. **tr_weighbridge_log** - Log aktivitas timbangan

### Key Fields
- **ConnectionType**: DUMMY, SERIAL, TCP, USB
- **IsDefault**: Konfigurasi default per cabang
- **IsActive**: Status aktif konfigurasi

## Implementation Files

### 1. Domain Layer
- `Bright.Domain/tm_weighbridge_config.vb` - Entity
- `Bright.Domain/Partial/tm_weighbridge_config.vb` - Business logic

### 2. Controller Layer
- `Bright.Controller/Master/tm_weighbridge_configController.vb` - CRUD operations

### 3. Service Layer
- `Bright.Helpers/WeighBridge/IWeighBridgeService.vb` - Interface
- `Bright.Helpers/WeighBridge/DummyWeighBridgeService.vb` - Dummy implementation
- `Bright.Helpers/WeighBridge/SerialWeighBridgeService.vb` - Serial implementation
- `Bright.Helpers/WeighBridge/TcpWeighBridgeService.vb` - TCP implementation
- `Bright.Helpers/WeighBridge/WeighBridgeManager.vb` - Service manager

### 4. UI Layer
- `Bright/Forms/Master/frmWeighbridgeConfig.aspx` - Main form
- `Bright/View/Master/ucWeighbridgeConfig_list.ascx` - List view
- `Bright/View/Master/ucWeighbridgeConfig_edit.ascx` - Edit view

### 5. API Layer
- `Bright/Api/WeighBridgeApiController.vb` - REST API
- `Bright/Scripts/weighbridge-helper.js` - JavaScript helper

## Connection Types

### 1. DUMMY (Development Mode)
```vb
' Features:
- Simulasi berat random
- Kontrol manual untuk testing
- Tidak memerlukan hardware
- Ideal untuk development dan demo
```

### 2. SERIAL
```vb
' Configuration:
- COM Port (COM1, COM2, etc.)
- Baud Rate (9600, 19200, etc.)
- Data Bits, Stop Bits, Parity
- Custom data format parsing
```

### 3. TCP/IP
```vb
' Configuration:
- IP Address
- Port number
- Custom protocol handling
- Network timeout settings
```

## Usage Examples

### 1. Basic Configuration
```vb
' Create new configuration
Dim config As New tm_weighbridge_config With {
    .Cabang_id = 1,
    .ScaleName = "Timbangan Utama",
    .ScaleId = "SCALE001",
    .ConnectionType = "DUMMY",
    .WeightUnit = "KG",
    .DecimalPlaces = 3,
    .IsActive = True,
    .IsDefault = True
}
```

### 2. Get Weighbridge Service
```vb
' Get service for specific branch
Dim manager = WeighBridgeManager.Instance
Dim service = manager.GetService(cabangId)

' Get current weight
Dim weightData = service.GetWeight()
Console.WriteLine($"Weight: {weightData.Weight} {weightData.Unit}")
```

### 3. Dummy Simulation
```vb
' Control dummy simulation
Dim dummyService = manager.GetDummyService(cabangId)
dummyService.StartWeighIn()  ' Start weigh-in simulation
dummyService.SetStableWeight(1500)  ' Set specific weight
dummyService.StartWeighOut()  ' Start weigh-out simulation
```

## API Endpoints

### Configuration Management
- `GET /api/weighbridge/status/{cabangId}` - Get status
- `POST /api/weighbridge/connect/{cabangId}` - Connect
- `POST /api/weighbridge/disconnect/{cabangId}` - Disconnect
- `POST /api/weighbridge/test/{configId}` - Test connection

### Weight Operations
- `GET /api/weighbridge/weight/{cabangId}` - Get current weight
- `GET /api/weighbridge/active-services` - Get all active services

### Dummy Controls
- `POST /api/weighbridge/dummy/weigh-in/{cabangId}` - Simulate weigh-in
- `POST /api/weighbridge/dummy/weigh-out/{cabangId}` - Simulate weigh-out
- `POST /api/weighbridge/dummy/set-weight/{cabangId}` - Set weight
- `GET /api/weighbridge/dummy/status/{cabangId}` - Get dummy status

## JavaScript Integration

### Initialize
```javascript
// Initialize weighbridge helper
WeighBridgeHelper.init(cabangId);

// Start monitoring
WeighBridgeHelper.startMonitoring(1000); // Every 1 second

// Get weight
WeighBridgeHelper.getWeight(function(data) {
    console.log('Weight:', data.weight, data.unit);
});
```

### Dummy Controls
```javascript
// Simulate weigh-in
WeighBridgeHelper.simulateWeighIn();

// Set specific weight
WeighBridgeHelper.setDummyWeight(1500);

// Simulate weigh-out
WeighBridgeHelper.simulateWeighOut();
```

## Testing Workflow

### 1. Setup Configuration
1. Buka form "Konfigurasi Jembatan Timbangan"
2. Klik "Tambah" untuk membuat konfigurasi baru
3. Pilih "DUMMY" untuk development
4. Isi nama dan scale ID
5. Set sebagai default jika diperlukan
6. Simpan konfigurasi

### 2. Test Connection
1. Klik "Test Koneksi" untuk verifikasi
2. Panel testing akan muncul jika berhasil
3. Klik "Connect" untuk terhubung
4. Monitor status koneksi

### 3. Test Weight Reading
1. Klik "Get Weight" untuk baca berat sekali
2. Klik "Start Monitoring" untuk monitoring real-time
3. Untuk DUMMY: gunakan simulation controls

### 4. Dummy Simulation
1. Klik "Simulate Weigh In" - berat akan naik bertahap
2. Klik "Simulate Weigh Out" - berat akan turun bertahap
3. Input manual weight dan klik "Set Weight"

## Integration with PO Receive Weighbridge

Konfigurasi ini akan digunakan oleh:
1. **tr_po_receive_weighbridge** - Form input data timbangan
2. **PO Receive Release** - Form posting ke GL dan stock
3. **Reporting** - Laporan transaksi timbangan

## Best Practices

### 1. Configuration
- Gunakan DUMMY untuk development dan testing
- Set hanya satu konfigurasi sebagai default per cabang
- Test koneksi sebelum menggunakan di production

### 2. Error Handling
- Selalu check `IsConnected()` sebelum operasi
- Handle timeout dan connection errors
- Log semua aktivitas untuk troubleshooting

### 3. Performance
- Gunakan singleton pattern untuk WeighBridgeManager
- Cache konfigurasi untuk menghindari query berulang
- Set appropriate timeout values

## Troubleshooting

### Common Issues
1. **Connection Failed**: Check konfigurasi port/IP
2. **Data Parse Error**: Verify data format configuration
3. **Timeout**: Adjust timeout settings
4. **Permission Denied**: Check COM port permissions

### Debug Mode
```vb
' Enable debug logging
Console.WriteLine($"[DEBUG] Connection: {service.ConnectionString}")
Console.WriteLine($"[DEBUG] Last Error: {service.LastError}")
```

## Future Enhancements
- Support untuk USB weighbridge
- Advanced data format parsing
- Real-time dashboard
- Historical data analysis
- Mobile app integration

# 🔧 **Web.Config Configuration - Weighbridge Settings**

## 🎯 **Implementation Overview**

Base URL weighbridge dan pengaturan lainnya sekarang dapat dikonfigurasi melalui web.config, memudahkan deployment dan penyesuaian di lapangan tanpa perlu mengubah kode.

## ✅ **Configuration Added to web.config**

### **AppSettings Section:**
```xml
<!-- Application Settings -->
<appSettings>
    <!-- Weighbridge Configuration -->
    <add key="WeighbridgeBaseUrl" value="http://127.0.0.1:8085" />
    <add key="WeighbridgeTimeout" value="5000" />
    <add key="WeighbridgeMonitorInterval" value="2000" />
    <add key="WeighbridgeRetryAttempts" value="3" />
    <add key="WeighbridgeAutoSaveStableWeight" value="true" />
</appSettings>
```

### **Configuration Parameters:**

| **Key** | **Default Value** | **Description** | **Valid Range** |
|---------|-------------------|-----------------|-----------------|
| `WeighbridgeBaseUrl` | `http://127.0.0.1:8085` | Base URL untuk weighbridge client API | Valid URL format |
| `WeighbridgeTimeout` | `5000` | Timeout untuk API calls (milliseconds) | 1 - 60000 |
| `WeighbridgeMonitorInterval` | `2000` | Interval monitoring weight (milliseconds) | 1 - 30000 |
| `WeighbridgeRetryAttempts` | `3` | Jumlah retry attempts untuk API calls | 0 - 10 |
| `WeighbridgeAutoSaveStableWeight` | `true` | Auto save weight saat stable during weighing | true/false |

## 🏗️ **Helper Class Implementation**

### **WeighbridgeConfigHelper.vb:**
```vb
Public Class WeighbridgeConfigHelper
    
    ' Read configuration from web.config
    Public Shared ReadOnly Property BaseUrl As String
        Get
            Return ConfigurationManager.AppSettings("WeighbridgeBaseUrl") ?? "http://127.0.0.1:8085"
        End Get
    End Property
    
    Public Shared ReadOnly Property Timeout As Integer
        Get
            Dim timeoutStr = ConfigurationManager.AppSettings("WeighbridgeTimeout")
            Dim timeout As Integer
            If Integer.TryParse(timeoutStr, timeout) Then
                Return timeout
            End If
            Return 5000 ' Default 5 seconds
        End Get
    End Property
    
    ' ... other properties
    
    ' Generate JavaScript configuration
    Public Shared Function GetConfigurationJson() As String
        Dim config = New With {
            .baseUrl = BaseUrl,
            .timeout = Timeout,
            .monitorInterval = MonitorInterval,
            .retryAttempts = RetryAttempts,
            .autoSaveStableWeight = AutoSaveStableWeight,
            .endpoints = New With {
                .getCurrentWeight = GetCurrentWeightUrl,
                .getStatus = GetStatusUrl
            }
        }
        Return Newtonsoft.Json.JsonConvert.SerializeObject(config)
    End Function
    
End Class
```

### **Key Features:**
- ✅ **Safe Parsing**: Handles invalid values with defaults
- ✅ **Validation**: Validates configuration ranges
- ✅ **JSON Export**: Exports configuration for JavaScript
- ✅ **Logging**: Provides configuration summary for debugging

## 🔄 **Server-Side Integration**

### **Code-Behind Implementation:**
```vb
Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
    If Not IsPostBack Then
        InitializeForm()
        RegisterWeighbridgeConfiguration()  ' ✅ New method
    End If
End Sub

Private Sub RegisterWeighbridgeConfiguration()
    Try
        ' Register weighbridge configuration for JavaScript
        Dim configJson = WeighbridgeConfigHelper.GetConfigurationJson()
        Dim script = $"var weighbridgeConfig = {configJson};"
        ClientScript.RegisterStartupScript(Me.GetType(), "WeighbridgeConfig", script, True)
        
        ' Log configuration for debugging
        System.Diagnostics.Debug.WriteLine(WeighbridgeConfigHelper.GetConfigurationSummary())
    Catch ex As Exception
        System.Diagnostics.Debug.WriteLine($"Error registering weighbridge configuration: {ex.Message}")
    End Try
End Sub
```

## 🌐 **JavaScript Integration**

### **Configuration Loading:**
```javascript
// Weighbridge configuration (loaded from web.config)
var config = {
    baseUrl: 'http://127.0.0.1:8085',
    timeout: 5000,
    monitorInterval: 2000,
    retryAttempts: 3,
    autoSaveStableWeight: true
};

// Load configuration from server if available
if (typeof weighbridgeConfig !== 'undefined') {
    config = weighbridgeConfig;
    console.log('Weighbridge configuration loaded from web.config:', config);
} else {
    console.log('Using default weighbridge configuration:', config);
}
```

### **Usage in Functions:**
```javascript
// Initialize weighbridge status
function initializeWeighbridgeForCabang(cabangId) {
    $.ajax({
        url: config.endpoints?.getStatus || (config.baseUrl + '/api/scale/GetStatus'),
        method: 'GET',
        timeout: config.timeout,  // ✅ From web.config
        // ...
    });
}

// Read current weight
function readCurrentWeight() {
    $.ajax({
        url: config.endpoints?.getCurrentWeight || (config.baseUrl + '/api/scale/GetCurrentWeight'),
        method: 'GET',
        timeout: config.timeout,  // ✅ From web.config
        // ...
    });
}

// Start monitoring with configurable interval
function startMonitoring() {
    weighbridgeTimer = setInterval(function() {
        readCurrentWeight();
    }, config.monitorInterval);  // ✅ From web.config
}

// Auto-save based on configuration
if (config.autoSaveStableWeight && weightData.isStable && currentWeighingStep !== 'idle') {
    saveWeight(weightData);  // ✅ Conditional based on web.config
}
```

## 📊 **Configuration Examples**

### **Development Environment:**
```xml
<appSettings>
    <add key="WeighbridgeBaseUrl" value="http://127.0.0.1:8085" />
    <add key="WeighbridgeTimeout" value="5000" />
    <add key="WeighbridgeMonitorInterval" value="1000" />
    <add key="WeighbridgeRetryAttempts" value="5" />
    <add key="WeighbridgeAutoSaveStableWeight" value="true" />
</appSettings>
```

### **Production Environment:**
```xml
<appSettings>
    <add key="WeighbridgeBaseUrl" value="http://*************:8085" />
    <add key="WeighbridgeTimeout" value="10000" />
    <add key="WeighbridgeMonitorInterval" value="3000" />
    <add key="WeighbridgeRetryAttempts" value="3" />
    <add key="WeighbridgeAutoSaveStableWeight" value="true" />
</appSettings>
```

### **Testing Environment:**
```xml
<appSettings>
    <add key="WeighbridgeBaseUrl" value="http://localhost:9999" />
    <add key="WeighbridgeTimeout" value="2000" />
    <add key="WeighbridgeMonitorInterval" value="500" />
    <add key="WeighbridgeRetryAttempts" value="1" />
    <add key="WeighbridgeAutoSaveStableWeight" value="false" />
</appSettings>
```

## 🛡️ **Validation and Error Handling**

### **Configuration Validation:**
```vb
Public Shared Function ValidateConfiguration() As List(Of String)
    Dim errors As New List(Of String)
    
    ' Validate Base URL
    If String.IsNullOrWhiteSpace(BaseUrl) Then
        errors.Add("WeighbridgeBaseUrl is required")
    ElseIf Not Uri.IsWellFormedUriString(BaseUrl, UriKind.Absolute) Then
        errors.Add("WeighbridgeBaseUrl is not a valid URL")
    End If
    
    ' Validate Timeout
    If Timeout <= 0 OrElse Timeout > 60000 Then
        errors.Add("WeighbridgeTimeout must be between 1 and 60000 milliseconds")
    End If
    
    ' ... other validations
    
    Return errors
End Function
```

### **JavaScript Fallback:**
```javascript
// Fallback to defaults if configuration is invalid
if (!config.baseUrl || config.baseUrl === '') {
    config.baseUrl = 'http://127.0.0.1:8085';
    console.warn('Invalid baseUrl, using default:', config.baseUrl);
}

if (!config.timeout || config.timeout <= 0) {
    config.timeout = 5000;
    console.warn('Invalid timeout, using default:', config.timeout);
}
```

## 🔧 **Deployment Scenarios**

### **Scenario 1: Local Development**
```xml
<!-- Developer machine with local weighbridge simulator -->
<add key="WeighbridgeBaseUrl" value="http://127.0.0.1:8085" />
<add key="WeighbridgeTimeout" value="5000" />
<add key="WeighbridgeMonitorInterval" value="1000" />
```

### **Scenario 2: Factory Floor**
```xml
<!-- Dedicated weighbridge PC on network -->
<add key="WeighbridgeBaseUrl" value="http://*************:8085" />
<add key="WeighbridgeTimeout" value="10000" />
<add key="WeighbridgeMonitorInterval" value="2000" />
```

### **Scenario 3: Multiple Scales**
```xml
<!-- Load balancer or primary scale -->
<add key="WeighbridgeBaseUrl" value="http://weighbridge.company.com:8085" />
<add key="WeighbridgeTimeout" value="8000" />
<add key="WeighbridgeMonitorInterval" value="3000" />
```

### **Scenario 4: Cloud Deployment**
```xml
<!-- Cloud-based weighbridge service -->
<add key="WeighbridgeBaseUrl" value="https://weighbridge-api.company.com" />
<add key="WeighbridgeTimeout" value="15000" />
<add key="WeighbridgeMonitorInterval" value="5000" />
```

## 📋 **Files Modified**

### **1. web.config**
- ✅ Added `<appSettings>` section with weighbridge configuration
- ✅ Configurable base URL, timeout, intervals, and behavior settings

### **2. WeighbridgeConfigHelper.vb (New)**
- ✅ Helper class untuk membaca konfigurasi dari web.config
- ✅ Type-safe property access dengan default values
- ✅ JSON serialization untuk JavaScript
- ✅ Configuration validation methods

### **3. ucPOReceiveWeighbridge_edit.ascx.vb**
- ✅ Added `RegisterWeighbridgeConfiguration()` method
- ✅ Exports configuration to JavaScript on page load
- ✅ Logging untuk debugging configuration

### **4. ucPOReceiveWeighbridge_edit.ascx**
- ✅ Updated JavaScript to use configuration object
- ✅ Dynamic URL construction from config
- ✅ Configurable timeouts and intervals
- ✅ Conditional auto-save based on configuration

## 🎯 **Benefits**

### **1. Flexibility**
- ✅ **Easy Deployment**: Change settings without code changes
- ✅ **Environment-Specific**: Different settings per environment
- ✅ **Field Adjustments**: IT staff can modify settings on-site

### **2. Maintainability**
- ✅ **Centralized Config**: All weighbridge settings in one place
- ✅ **Type Safety**: Helper class ensures proper data types
- ✅ **Validation**: Built-in validation prevents invalid configurations

### **3. Debugging**
- ✅ **Configuration Logging**: Settings logged for troubleshooting
- ✅ **Fallback Values**: Graceful handling of missing/invalid settings
- ✅ **Console Output**: JavaScript shows loaded configuration

## 🔍 **Testing Configuration**

### **1. Verify Configuration Loading**
```javascript
// Check browser console for:
console.log('Weighbridge configuration loaded from web.config:', config);

// Expected output:
{
    baseUrl: "http://127.0.0.1:8085",
    timeout: 5000,
    monitorInterval: 2000,
    retryAttempts: 3,
    autoSaveStableWeight: true,
    endpoints: {
        getCurrentWeight: "http://127.0.0.1:8085/api/scale/GetCurrentWeight",
        getStatus: "http://127.0.0.1:8085/api/scale/GetStatus"
    }
}
```

### **2. Test Different Configurations**
```xml
<!-- Test with different base URL -->
<add key="WeighbridgeBaseUrl" value="http://*************:9999" />

<!-- Verify JavaScript uses new URL -->
// Should see AJAX calls to: http://*************:9999/api/scale/GetCurrentWeight
```

### **3. Validate Error Handling**
```xml
<!-- Test with invalid configuration -->
<add key="WeighbridgeBaseUrl" value="invalid-url" />
<add key="WeighbridgeTimeout" value="abc" />

<!-- Should fall back to defaults and log warnings -->
```

## 🔮 **Future Enhancements**

### **Configuration UI**
```vb
' Could create admin page for configuration management
Public Class WeighbridgeConfigAdmin
    Public Sub UpdateConfiguration(baseUrl As String, timeout As Integer)
        ' Update web.config programmatically
        ' Validate settings
        ' Apply changes
    End Sub
End Class
```

### **Runtime Configuration Reload**
```javascript
// Could implement configuration refresh without page reload
function reloadConfiguration() {
    $.ajax({
        url: '/api/weighbridge/config',
        success: function(newConfig) {
            config = newConfig;
            console.log('Configuration reloaded:', config);
        }
    });
}
```

The Web.Config configuration system has been **IMPLEMENTED** with full flexibility for field deployment! 🎯✅

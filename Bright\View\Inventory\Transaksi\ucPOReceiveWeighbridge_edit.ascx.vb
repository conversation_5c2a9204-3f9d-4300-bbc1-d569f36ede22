Imports System
Imports System.Web.UI
Imports Bright.Controller
Imports Bright.Domain
Imports Bright.MVVM
Imports Bright.Helpers
Imports Newtonsoft.Json
Imports DevExpress.Web
Imports System.Globalization
Imports System.Linq

Public Class ucPOReceiveWeighbridge_edit
    Inherits UserControl

    <Inject>
    Property Controller As Itr_po_receive_weighbridgeEditController

    ' Form controls - declared based on markup to avoid designer conflicts
    Protected WithEvents hf_receive_id As System.Web.UI.WebControls.HiddenField
    Protected WithEvents ASPxFormLayout1 As ASPxFormLayout

    ' Basic Information
    Protected WithEvents txt_no_receive As ASPxTextBox
    Protected WithEvents dt_tanggal As ASPxDateEdit
    Protected WithEvents txt_status As ASPxTextBox
    Protected WithEvents cb_area As ASPxComboBox
    Protected WithEvents cb_cabang As ASPxComboBox
    Protected WithEvents cb_lokasi As ASPxComboBox
    Protected WithEvents cb_po As ASPxComboBox
    Protected WithEvents btn_create_from_po As ASPxButton
    Protected WithEvents txt_keterangan As ASPxMemo

    ' Vehicle Information
    Protected WithEvents cb_kendaraan As ASPxComboBox
    Protected WithEvents btn_add_vehicle As ASPxButton
    Protected WithEvents txt_driver_name As ASPxTextBox
    Protected WithEvents txt_driver_phone As ASPxTextBox

    ' Weighbridge Control Panel
    Protected WithEvents btn_weigh_in As ASPxButton
    Protected WithEvents btn_weigh_out As ASPxButton
    Protected WithEvents btn_monitor As ASPxButton
    Protected WithEvents btn_test_weight As ASPxButton

    ' Weight Information
    Protected WithEvents txt_gross_weight As ASPxSpinEdit
    Protected WithEvents txt_tare_weight As ASPxSpinEdit
    Protected WithEvents txt_net_weight As ASPxSpinEdit
    Protected WithEvents txt_weight_diff As ASPxTextBox
    Protected WithEvents dt_weigh_in_time As ASPxDateEdit
    Protected WithEvents dt_weigh_out_time As ASPxDateEdit
    Protected WithEvents txt_duration As ASPxTextBox
    Protected WithEvents cb_quality_status As ASPxComboBox

    ' Posting Information
    Protected WithEvents chk_posted As ASPxCheckBox
    Protected WithEvents txt_posted_by As ASPxTextBox
    Protected WithEvents dt_posted_date As ASPxDateEdit
    Protected WithEvents txt_created_by As ASPxTextBox
    Protected WithEvents dt_created_date As ASPxDateEdit
    Protected WithEvents txt_modified_by As ASPxTextBox
    Protected WithEvents dt_modified_date As ASPxDateEdit

    ' Action Buttons
    Protected WithEvents btn_save As ASPxButton
    Protected WithEvents btn_post_transaction As ASPxButton

    <EventSubscription>
    Public Sub OnEditChanged(sender As Object, e As EventArgs)
        Try
            If Controller.SelectedItem IsNot Nothing Then
                Visible = True
                LoadDataToForm()
                LoadComboBoxes()
                UpdateFormState()
            Else
                Visible = False
                ClearForm()
            End If
        Catch ex As Exception
            ' Log error and show user-friendly message
            System.Diagnostics.Debug.WriteLine($"Error in OnEditChanged: {ex.Message}")
            ' Could add logging here
        End Try
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            InitializeForm()
        End If
    End Sub

    Protected Sub Page_PreRender(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.PreRender
        ' Load combo boxes during PreRender to ensure all controls are initialized
        If Not IsPostBack Then
            LoadComboBoxes()
        End If
    End Sub

    Private Sub InitializeForm()
        Try
            ' Set default values
            If dt_tanggal IsNot Nothing Then
                dt_tanggal.Date = DateTime.Today
            End If

            ' Don't load combo boxes here - do it in PreRender
            ' LoadComboBoxes()

            ' Set initial form state
            UpdateFormState()
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error initializing form: {ex.Message}")
        End Try
    End Sub

    Private Sub LoadComboBoxes()
        Try
            ' Load Area with enhanced safety checks
            LoadComboBoxSafely(cb_area, "Area", Function() Controller?.AreaList)

            ' Load Cabang
            LoadComboBoxSafely(cb_cabang, "Cabang", Function() Controller?.CabangList)

            ' Load Lokasi
            LoadComboBoxSafely(cb_lokasi, "Lokasi", Function() Controller?.LokasiList)

            ' Load PO
            LoadComboBoxSafely(cb_po, "PO", Function() Controller?.POList)

            ' Load Kendaraan
            LoadComboBoxSafely(cb_kendaraan, "Kendaraan", Function() Controller?.KendaraanList)

            ' Load Quality Status
            If cb_quality_status IsNot Nothing AndAlso Controller?.StatusList IsNot Nothing Then
                Try
                    cb_quality_status.DataSourceID = String.Empty
                    cb_quality_status.Items.Clear()
                    For Each status In Controller.StatusList
                        cb_quality_status.Items.Add(status, status)
                    Next
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine($"Error loading Quality Status: {ex.Message}")
                End Try
            End If

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error loading combo boxes: {ex.Message}")
            ' Log the specific error for debugging
            If ex.Message.Contains("DataSource") Then
                System.Diagnostics.Debug.WriteLine("DataSource conflict detected. Using alternative loading method.")
            End If
        End Try
    End Sub

    Private Sub LoadComboBoxSafely(comboBox As ASPxComboBox, name As String, dataSourceFunc As Func(Of Object))
        If comboBox Is Nothing Then
            System.Diagnostics.Debug.WriteLine($"{name} combo box is null")
            Return
        End If

        Try
            Dim dataSource = dataSourceFunc?.Invoke()
            If dataSource Is Nothing Then
                System.Diagnostics.Debug.WriteLine($"{name} data source is null")
                Return
            End If

            ' Clear any existing data source settings
            comboBox.DataSourceID = String.Empty
            comboBox.DataSource = Nothing

            ' Small delay to ensure control is ready
            System.Threading.Thread.Sleep(10)

            ' Set new data source
            comboBox.DataSource = dataSource
            comboBox.DataBind()

            System.Diagnostics.Debug.WriteLine($"{name} combo box loaded successfully")

        Catch ex As InvalidOperationException When ex.Message.Contains("DataSource")
            System.Diagnostics.Debug.WriteLine($"DataSource conflict for {name}: {ex.Message}")
            ' Try alternative method - clear everything and retry
            Try
                comboBox.DataSourceID = Nothing
                comboBox.DataSource = Nothing
                comboBox.Items.Clear()
                System.Threading.Thread.Sleep(50)
                comboBox.DataSource = dataSourceFunc?.Invoke()
                comboBox.DataBind()
            Catch retryEx As Exception
                System.Diagnostics.Debug.WriteLine($"Retry failed for {name}: {retryEx.Message}")
            End Try
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error loading {name} combo box: {ex.Message}")
        End Try
    End Sub

    Private Sub LoadDataToForm()
        Try
            Dim item = Controller.SelectedItem
            If item Is Nothing Then Return

            ' Basic Information
            SetControlValue(txt_no_receive, item.NoReceive)
            SetControlValue(dt_tanggal, item.Tanggal)
            SetControlValue(cb_area, item.Area_id)
            SetControlValue(cb_cabang, item.Cabang_id)
            SetControlValue(cb_lokasi, item.Lokasi_id)
            SetControlValue(cb_kendaraan, item.Kendaraan_id)
            SetControlValue(txt_driver_name, item.DriverName)
            SetControlValue(txt_driver_phone, item.DriverPhone)
            SetControlValue(txt_keterangan, item.Keterangan)

            ' Weight Information
            SetControlValue(txt_gross_weight, item.GrossWeight)
            SetControlValue(txt_tare_weight, item.TareWeight)
            SetControlValue(txt_net_weight, item.NetWeight)
            SetControlValue(dt_weigh_in_time, item.WeighInTime)
            SetControlValue(dt_weigh_out_time, item.WeighOutTime)

            ' Calculate and display duration
            UpdateDurationDisplay()

            ' Status Information
            SetControlValue(txt_status, item.Status)
            SetControlValue(chk_posted, item.Posted)
            SetControlValue(txt_posted_by, item.PostedBy)
            SetControlValue(dt_posted_date, item.PostedDate)
            SetControlValue(txt_created_by, item.CreatedBy)
            SetControlValue(dt_created_date, item.CreatedDate)
            SetControlValue(txt_modified_by, item.ModifiedBy)
            SetControlValue(dt_modified_date, item.ModifiedDate)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error loading data to form: {ex.Message}")
        End Try
    End Sub

    Private Sub SetControlValue(control As Object, value As Object)
        Try
            If control Is Nothing Then Return

            Select Case True
                Case TypeOf control Is ASPxTextBox
                    DirectCast(control, ASPxTextBox).Text = If(value?.ToString(), "")

                Case TypeOf control Is ASPxMemo
                    DirectCast(control, ASPxMemo).Text = If(value?.ToString(), "")

                Case TypeOf control Is ASPxDateEdit
                    Dim dateControl = DirectCast(control, ASPxDateEdit)
                    If TypeOf value Is DateTime? Then
                        dateControl.Date = DirectCast(value, DateTime?)
                    ElseIf TypeOf value Is DateTime Then
                        dateControl.Date = DirectCast(value, DateTime)
                    Else
                        dateControl.Date = Nothing
                    End If

                Case TypeOf control Is ASPxComboBox
                    Dim comboControl = DirectCast(control, ASPxComboBox)
                    If value IsNot Nothing Then
                        comboControl.Value = value
                    Else
                        comboControl.Value = Nothing
                    End If

                Case TypeOf control Is ASPxSpinEdit
                    Dim spinControl = DirectCast(control, ASPxSpinEdit)
                    If TypeOf value Is Decimal? Then
                        spinControl.Value = DirectCast(value, Decimal?)
                    ElseIf TypeOf value Is Decimal Then
                        spinControl.Value = DirectCast(value, Decimal)
                    Else
                        spinControl.Value = Nothing
                    End If

                Case TypeOf control Is ASPxCheckBox
                    Dim checkControl = DirectCast(control, ASPxCheckBox)
                    If TypeOf value Is Boolean? Then
                        checkControl.Checked = DirectCast(value, Boolean?).GetValueOrDefault(False)
                    ElseIf TypeOf value Is Boolean Then
                        checkControl.Checked = DirectCast(value, Boolean)
                    Else
                        checkControl.Checked = False
                    End If
            End Select

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error setting control value: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateDurationDisplay()
        Try
            If txt_duration IsNot Nothing AndAlso Controller.SelectedItem IsNot Nothing Then
                Dim item = Controller.SelectedItem
                If item.WeighInTime.HasValue AndAlso item.WeighOutTime.HasValue Then
                    Dim duration = item.WeighOutTime.Value - item.WeighInTime.Value
                    txt_duration.Text = $"{duration.Hours:D2}:{duration.Minutes:D2}:{duration.Seconds:D2}"
                Else
                    txt_duration.Text = ""
                End If
            End If
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error updating duration display: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateFormState()
        Try
            Dim item = Controller.SelectedItem
            If item Is Nothing Then Return

            Dim isPosted = item.Posted.GetValueOrDefault(False)
            Dim status = If(item.Status, "DRAFT")

            ' Disable editing if posted
            Dim isEditable = Not isPosted

            ' Enable/disable controls based on status
            EnableFormControls(isEditable)

            ' Update weighbridge control panel state
            UpdateWeighbridgeControlState(status)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error updating form state: {ex.Message}")
        End Try
    End Sub

    Private Sub EnableFormControls(enabled As Boolean)
        Try
            ' Basic controls
            If dt_tanggal IsNot Nothing Then dt_tanggal.ReadOnly = Not enabled
            If cb_area IsNot Nothing Then cb_area.ReadOnly = Not enabled
            If cb_cabang IsNot Nothing Then cb_cabang.ReadOnly = Not enabled
            If cb_lokasi IsNot Nothing Then cb_lokasi.ReadOnly = Not enabled
            If cb_po IsNot Nothing Then cb_po.ReadOnly = Not enabled
            If cb_kendaraan IsNot Nothing Then cb_kendaraan.ReadOnly = Not enabled
            If txt_driver_name IsNot Nothing Then txt_driver_name.ReadOnly = Not enabled
            If txt_driver_phone IsNot Nothing Then txt_driver_phone.ReadOnly = Not enabled
            If txt_keterangan IsNot Nothing Then txt_keterangan.ReadOnly = Not enabled

            ' Weight controls are always readonly (controlled by weighbridge)
            If txt_gross_weight IsNot Nothing Then txt_gross_weight.ReadOnly = True
            If txt_tare_weight IsNot Nothing Then txt_tare_weight.ReadOnly = True
            If txt_net_weight IsNot Nothing Then txt_net_weight.ReadOnly = True
            If dt_weigh_in_time IsNot Nothing Then dt_weigh_in_time.ReadOnly = True
            If dt_weigh_out_time IsNot Nothing Then dt_weigh_out_time.ReadOnly = True

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error enabling/disabling form controls: {ex.Message}")
        End Try
    End Sub

    Private Sub UpdateWeighbridgeControlState(status As String)
        Try
            ' This will be called from JavaScript to update weighbridge control panel
            Dim script As String = $"updateWeighbridgeControlState('{status}');"
            ScriptManager.RegisterStartupScript(Me, Me.GetType(), "updateWeighbridgeState", script, True)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error updating weighbridge control state: {ex.Message}")
        End Try
    End Sub

    Private Sub ClearForm()
        Try
            ' Clear all form controls
            SetControlValue(txt_no_receive, "")
            SetControlValue(dt_tanggal, DateTime.Today)
            SetControlValue(cb_area, Nothing)
            SetControlValue(cb_cabang, Nothing)
            SetControlValue(cb_lokasi, Nothing)
            SetControlValue(cb_po, Nothing)
            SetControlValue(cb_kendaraan, Nothing)
            SetControlValue(txt_driver_name, "")
            SetControlValue(txt_driver_phone, "")
            SetControlValue(txt_keterangan, "")
            SetControlValue(txt_gross_weight, Nothing)
            SetControlValue(txt_tare_weight, Nothing)
            SetControlValue(txt_net_weight, Nothing)
            SetControlValue(dt_weigh_in_time, Nothing)
            SetControlValue(dt_weigh_out_time, Nothing)
            SetControlValue(txt_duration, "")
            SetControlValue(txt_status, "DRAFT")
            SetControlValue(chk_posted, False)
            SetControlValue(txt_posted_by, "")
            SetControlValue(dt_posted_date, Nothing)
            SetControlValue(txt_created_by, "")
            SetControlValue(dt_created_date, Nothing)
            SetControlValue(txt_modified_by, "")
            SetControlValue(dt_modified_date, Nothing)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error clearing form: {ex.Message}")
        End Try
    End Sub

    ' Prepare data before saving
    Public Sub SavingPrepare()
        Try
            If Controller.SelectedItem Is Nothing Then
                Throw New InvalidOperationException("No item selected for saving")
            End If

            With Controller.SelectedItem
                ' Basic Information
                .NoReceive = GetControlValue(Of String)(txt_no_receive)
                .Tanggal = GetControlValue(Of DateTime)(dt_tanggal)
                .Area_id = GetControlValue(Of Integer)(cb_area)
                .Cabang_id = GetControlValue(Of Integer)(cb_cabang)
                .Lokasi_id = GetControlValue(Of Integer)(cb_lokasi)
                .Kendaraan_id = GetControlValue(Of Integer)(cb_kendaraan)
                .DriverName = GetControlValue(Of String)(txt_driver_name)
                .DriverPhone = GetControlValue(Of String)(txt_driver_phone)
                .Keterangan = GetControlValue(Of String)(txt_keterangan)

                ' Weight Information (these are usually set by weighbridge, but allow manual override)
                .GrossWeight = GetControlValue(Of Decimal?)(txt_gross_weight)
                .TareWeight = GetControlValue(Of Decimal?)(txt_tare_weight)
                .NetWeight = GetControlValue(Of Decimal?)(txt_net_weight)
                .WeighInTime = GetControlValue(Of DateTime?)(dt_weigh_in_time)
                .WeighOutTime = GetControlValue(Of DateTime?)(dt_weigh_out_time)

                ' Status
                .Status = GetControlValue(Of String)(txt_status)

                ' Calculate net weight if both gross and tare are available
                If .GrossWeight.HasValue AndAlso .TareWeight.HasValue Then
                    .NetWeight = .GrossWeight.Value - .TareWeight.Value
                    ' Update the display
                    SetControlValue(txt_net_weight, .NetWeight)
                End If

                ' Set audit fields
                If .Id = 0 Then ' New record
                    .CreatedBy = AuthHelper.GetLoggedInUserInfo.UserName
                    .CreatedDate = DateTime.Now
                Else ' Existing record
                    .ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                    .ModifiedDate = DateTime.Now
                End If
            End With

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in SavingPrepare: {ex.Message}")
            Throw New Exception($"Error preparing data for save: {ex.Message}", ex)
        End Try
    End Sub

    Private Function GetControlValue(Of T)(control As Object) As T
        Try
            If control Is Nothing Then Return Nothing

            Select Case True
                Case TypeOf control Is ASPxTextBox
                    Dim textValue = DirectCast(control, ASPxTextBox).Text
                    Return ConvertValue(Of T)(textValue)

                Case TypeOf control Is ASPxMemo
                    Dim memoValue = DirectCast(control, ASPxMemo).Text
                    Return ConvertValue(Of T)(memoValue)

                Case TypeOf control Is ASPxDateEdit
                    Dim dateValue = DirectCast(control, ASPxDateEdit).Date
                    Return ConvertValue(Of T)(dateValue)

                Case TypeOf control Is ASPxComboBox
                    Dim comboValue = DirectCast(control, ASPxComboBox).Value
                    Return ConvertValue(Of T)(comboValue)

                Case TypeOf control Is ASPxSpinEdit
                    Dim spinValue = DirectCast(control, ASPxSpinEdit).Value
                    Return ConvertValue(Of T)(spinValue)

                Case TypeOf control Is ASPxCheckBox
                    Dim checkValue = DirectCast(control, ASPxCheckBox).Checked
                    Return ConvertValue(Of T)(checkValue)

                Case Else
                    Return Nothing
            End Select

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting control value: {ex.Message}")
            Return Nothing
        End Try
    End Function

    Private Function ConvertValue(Of T)(value As Object) As T
        Try
            If value Is Nothing Then Return Nothing

            Dim targetType = GetType(T)

            ' Handle nullable types
            If targetType.IsGenericType AndAlso targetType.GetGenericTypeDefinition() = GetType(Nullable(Of )) Then
                If value Is Nothing OrElse value Is DBNull.Value Then
                    Return Nothing
                End If
                targetType = Nullable.GetUnderlyingType(targetType)
            End If

            ' Handle string conversion
            If targetType = GetType(String) Then
                Return DirectCast(CObj(value?.ToString()), T)
            End If

            ' Handle other type conversions
            Return DirectCast(Convert.ChangeType(value, targetType, CultureInfo.InvariantCulture), T)

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error converting value: {ex.Message}")
            Return Nothing
        End Try
    End Function

    ' Process weight save from weighbridge
    Public Sub ProcessWeightSave(weightData As frmPOReceiveWeighbridge.WeightData)
        Try
            If Controller.SelectedItem Is Nothing Then
                Throw New InvalidOperationException("No item selected for weight save")
            End If

            If weightData Is Nothing Then
                Throw New ArgumentNullException(NameOf(weightData), "Weight data cannot be null")
            End If

            ' Convert to domain weight data
            Dim domainWeightData = weightData.ToDomainWeightData()

            With Controller.SelectedItem
                Select Case weightData.step?.ToUpper()
                    Case "WEIGH_IN"
                        .GrossWeight = domainWeightData.Weight
                        .WeighInTime = domainWeightData.Timestamp
                        .Status = "WEIGH_IN"

                        ' Update form display
                        SetControlValue(txt_gross_weight, .GrossWeight)
                        SetControlValue(dt_weigh_in_time, .WeighInTime)
                        SetControlValue(txt_status, .Status)

                    Case "WEIGH_OUT"
                        .TareWeight = domainWeightData.Weight
                        .WeighOutTime = domainWeightData.Timestamp
                        .Status = "COMPLETED"

                        ' Calculate net weight
                        If .GrossWeight.HasValue Then
                            .NetWeight = .GrossWeight.Value - domainWeightData.Weight
                        End If

                        ' Update form display
                        SetControlValue(txt_tare_weight, .TareWeight)
                        SetControlValue(dt_weigh_out_time, .WeighOutTime)
                        SetControlValue(txt_net_weight, .NetWeight)
                        SetControlValue(txt_status, .Status)

                        ' Update duration
                        UpdateDurationDisplay()

                    Case Else
                        Throw New ArgumentException($"Invalid weighing step: {weightData.step}")
                End Select

                ' Set audit fields
                .ModifiedBy = AuthHelper.GetLoggedInUserInfo.UserName
                .ModifiedDate = DateTime.Now
            End With

            ' Update form state
            UpdateFormState()

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in ProcessWeightSave: {ex.Message}")
            Throw New Exception($"Error processing weight save: {ex.Message}", ex)
        End Try
    End Sub

    ' Start weighing process
    Public Sub StartWeighingProcess(stepName As String)
        Try
            If Controller.SelectedItem Is Nothing Then
                Throw New InvalidOperationException("No item selected for weighing process")
            End If

            ' Validate prerequisites based on step
            Select Case stepName?.ToUpper()
                Case "WEIGH_IN"
                    ' Validate basic information is complete
                    ValidateBasicInformation()

                Case "WEIGH_OUT"
                    ' Validate that weigh-in is completed
                    If Not Controller.SelectedItem.GrossWeight.HasValue OrElse
                       Not Controller.SelectedItem.WeighInTime.HasValue Then
                        Throw New InvalidOperationException("Weigh-in must be completed before weigh-out")
                    End If

                Case Else
                    Throw New ArgumentException($"Invalid weighing step: {stepName}")
            End Select

            ' Update status and form
            Controller.SelectedItem.Status = stepName.ToUpper()
            SetControlValue(txt_status, Controller.SelectedItem.Status)
            UpdateFormState()

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in StartWeighingProcess: {ex.Message}")
            Throw New Exception($"Error starting weighing process: {ex.Message}", ex)
        End Try
    End Sub

    ' Stop weighing process
    Public Sub StopWeighingProcess()
        Try
            ' Reset weighing state
            Dim script As String = "stopMonitoring();"
            ScriptManager.RegisterStartupScript(Me, Me.GetType(), "stopWeighing", script, True)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error in StopWeighingProcess: {ex.Message}")
        End Try
    End Sub

    Private Sub ValidateBasicInformation()
        Dim errors As New List(Of String)

        If GetControlValue(Of Integer)(cb_cabang) <= 0 Then
            errors.Add("Branch must be selected")
        End If

        If GetControlValue(Of Integer)(cb_lokasi) <= 0 Then
            errors.Add("Location must be selected")
        End If

        If GetControlValue(Of Integer)(cb_kendaraan) <= 0 Then
            errors.Add("Vehicle must be selected")
        End If

        If String.IsNullOrWhiteSpace(GetControlValue(Of String)(txt_driver_name)) Then
            errors.Add("Driver name must be filled")
        End If

        If errors.Any() Then
            Throw New ValidationException($"Validation failed: {String.Join("; ", errors)}")
        End If
    End Sub

    ' Public method to refresh form data
    Public Sub RefreshForm()
        Try
            LoadDataToForm()
            UpdateFormState()
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error refreshing form: {ex.Message}")
        End Try
    End Sub

    ' Public method to get current cabang ID for JavaScript
    Public Function GetCurrentCabangId() As Integer
        Try
            Return GetControlValue(Of Integer)(cb_cabang)
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"Error getting current cabang ID: {ex.Message}")
            Return 0
        End Try
    End Function

End Class

' Custom exception for validation errors
Public Class ValidationException
    Inherits Exception

    Public Sub New(message As String)
        MyBase.New(message)
    End Sub

    Public Sub New(message As String, innerException As Exception)
        MyBase.New(message, innerException)
    End Sub
End Class


﻿<%@ Control Language="vb" AutoEventWireup="false" CodeBehind="ucBankEntryUnrelease_list.ascx.vb" Inherits="Bright.ucBankEntryUnrelease_list" %>
<%@ Register assembly="DevExpress.Web.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" namespace="DevExpress.Data.Linq" tagprefix="dx" %>

<table style="width: 100%;">
    <tr>
        <td style="width: 150px; font-weight: bold">Start Date</td>
        <td style="width: 170px">
            <dx:ASPxDateEdit ID="txt_startDate" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                <ClientSideEvents ValueChanged="function(s, e) {
	grd_bank.Refresh();
}" />
            </dx:ASPxDateEdit>
        </td>
        <td style="font-weight: bold; width: 150px">End Date</td>
        <td>
            <dx:ASPxDateEdit ID="txt_endDate" runat="server" DisplayFormatString="dd MMM yyyy" EditFormat="Custom" EditFormatString="dd-MM-yyyy">
                <ClientSideEvents ValueChanged="function(s, e) {
	grd_bank.Refresh();
}" />
            </dx:ASPxDateEdit>
        </td>
    </tr>
    </table>
<dx:ASPxGridView ID="ASPxGridView1" runat="server" AutoGenerateColumns="False" DataSourceID="EntityServerModeDataSource1" KeyFieldName="Id" Width="100%" ClientInstanceName="grd_bank">
    <ClientSideEvents CustomButtonClick="function(s, e) {
	var rowKey = s.GetRowKey(s.GetFocusedRowIndex());
	if(e.buttonID!='btn_print'){
		if(e.buttonID=='btn_delete'){
			var b=confirm('Are you sure to delete this item?');
			if(b){
				cp_matauang.PerformCallback(e.buttonID+';'+rowKey);
			}
		}else{cp_matauang.PerformCallback(e.buttonID+';'+rowKey);}
	}else{wd1.Show();}
}" ToolbarItemClick="function(s, e) {
switch (e.item.name) { 
case 'btn_addnew':
cp_matauang.PerformCallback('new'); 
break;
}
}" />
    <SettingsAdaptivity AdaptivityMode="HideDataCells" HideDataCellsAtWindowInnerWidth="600">
    </SettingsAdaptivity>
    <SettingsPager>
        <AllButton Visible="True">
        </AllButton>
        <PageSizeItemSettings Visible="True">
        </PageSizeItemSettings>
    </SettingsPager>
    <Settings ShowFooter="True" />
    <SettingsBehavior AllowFocusedRow="True" />

<SettingsPopup>
<HeaderFilter MinHeight="140px"></HeaderFilter>
</SettingsPopup>

    <SettingsSearchPanel Visible="True" />
    <SettingsExport EnableClientSideExportAPI="True">
    </SettingsExport>
    <Columns>
        <dx:GridViewCommandColumn VisibleIndex="0">
            <CustomButtons>
                <dx:GridViewCommandColumnCustomButton ID="btn_unrelease" Text="UnRelease">
                    <Image IconID="outlookinspired_paymentunpaid_svg_16x16">
                    </Image>
                </dx:GridViewCommandColumnCustomButton>
            </CustomButtons>
        </dx:GridViewCommandColumn>
        <dx:GridViewDataTextColumn FieldName="tm_area.KodeArea" VisibleIndex="1" Caption="Area">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="tm_cabang.KodeCabang" VisibleIndex="2" Caption="Cabang">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="NoTransaksi" VisibleIndex="3">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataDateColumn FieldName="Tanggal" VisibleIndex="4">
            <PropertiesDateEdit DisplayFormatInEditMode="True" DisplayFormatString="dd MMM yyyy">
            </PropertiesDateEdit>
        </dx:GridViewDataDateColumn>
        <dx:GridViewDataTextColumn Caption="Jenis trans" FieldName="tm_jurnal_model.Kode" VisibleIndex="5">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn Caption="Kurs" FieldName="tm_matauang.Singkatan" VisibleIndex="6">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn Caption="Bank" FieldName="tm_bank.NamaAkunBank" VisibleIndex="7">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Keterangan" VisibleIndex="10">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="NoJurnal" VisibleIndex="18">
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="Total" VisibleIndex="8">
            <PropertiesTextEdit DisplayFormatInEditMode="True" DisplayFormatString="n2">
            </PropertiesTextEdit>
        </dx:GridViewDataTextColumn>
        <dx:GridViewDataTextColumn FieldName="CheqNumber" VisibleIndex="9">
        </dx:GridViewDataTextColumn>
    </Columns>
    <Toolbars>
        <dx:GridViewToolbar>
            <Items>
                <dx:GridViewToolbarItem Command="ShowFilterRow">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem Command="ExportToXls">
                </dx:GridViewToolbarItem>
                <dx:GridViewToolbarItem Command="ExportToXlsx">
                </dx:GridViewToolbarItem>
            </Items>
        </dx:GridViewToolbar>
    </Toolbars>
    <TotalSummary>
        <dx:ASPxSummaryItem DisplayFormat="{0:n2}" FieldName="Total" ShowInColumn="Total" SummaryType="Sum" />
    </TotalSummary>
</dx:ASPxGridView>
<dx:EntityServerModeDataSource ID="EntityServerModeDataSource1" runat="server" ContextTypeName="Bright.Domain.BrightEntities" TableName="tr_bank_entry">
</dx:EntityServerModeDataSource>

/* ========================================
   MODERN WEIGHBRIDGE DASHBOARD DESIGN
   ======================================== */

/* Main Dashboard Container */
.weighbridge-dashboard {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Left Panel - Main Operations */
.main-operations-panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Right Panel - Weight Display & Controls */
.weight-control-panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;
    height: fit-content;
}

/* Live Weight Display */
.live-weight-display {
    background: linear-gradient(135deg, #00b4db 0%, #0083b0 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 25px;
    box-shadow: 0 5px 15px rgba(0, 180, 219, 0.3);
}

.weight-value {
    font-size: 3.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.weight-unit {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-left: 10px;
}

.weight-status {
    font-size: 1rem;
    margin-top: 10px;
    opacity: 0.9;
}

.weight-stability {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 10px;
    animation: pulse 2s infinite;
}

.weight-stable {
    background: #28a745;
}

.weight-unstable {
    background: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Status Cards */
.status-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
}

.status-card {
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    color: white;
    font-weight: 600;
}

.card-gross {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.card-tare {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.card-net {
    background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

.card-status {
    background: linear-gradient(135deg, #1dd1a1 0%, #10ac84 100%);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.btn-weighbridge {
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-weigh-in {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
}

.btn-weigh-out {
    background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
    color: white;
}

.btn-add-manual {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
    color: white;
}

.btn-reset {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

.btn-weighbridge:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-weighbridge:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Process Flow Indicator */
.process-flow {
    margin-bottom: 25px;
}

.flow-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.flow-step {
    background: #e9ecef;
    color: #6c757d;
    padding: 12px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 2;
    position: relative;
}

.flow-step.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.flow-step.completed {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
}

.flow-connector {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: #e9ecef;
    z-index: 1;
}

/* Data Grid Modern Style */
.modern-grid {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.grid-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Alert Styles */
.alert-modern {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
}

.alert-error {
    background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
    color: white;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .weighbridge-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
    
    .weight-value {
        font-size: 2.5rem;
    }
    
    .status-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Feedback */
.feedback-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.feedback-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feedback-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.feedback-success .feedback-icon {
    color: #00b894;
}

.feedback-error .feedback-icon {
    color: #d63031;
}
